﻿using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace Life.Data
{
    public class AppDbContext : IdentityDbContext<User>
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        public DbSet<Reminder> Reminders { get; set; }
        public DbSet<GeneralItem> GeneralItems { get; set; }
        public DbSet<DrivingLicence> DrivingLicences { get; set; }
        public DbSet<Passport> Passports { get; set; }
        public DbSet<VehicleTax> VehicleTaxes { get; set; }
        public DbSet<VehicleInsurancePolicy> VehicleInsurancePolicies { get; set; }
        public DbSet<VehicleMot> VehicleMots { get; set; }
        public DbSet<Vehicle> Vehicles { get; set; }
        public DbSet<VehicleBreakdownPolicy> VehicleBreakdownPolicies { get; set; }
        public DbSet<AddressInsurancePolicy> AddressInsurancePolicies { get; set; }
        public DbSet<Tenancy> Tenancies { get; set; }
        public DbSet<UtilityBill> UtilityBills { get; set; }
        public DbSet<Mortgage> Mortgages { get; set; }
        public DbSet<CurrentAccount> CurrentAccounts { get; set; }
        public DbSet<CreditCard> CreditCards { get; set; }
        public DbSet<Loan> Loans { get; set; }
        public DbSet<SavingsAccount> SavingsAccounts { get; set; }
        public DbSet<Pension> Pensions { get; set; }
        public DbSet<LifeInsurancePolicy> LifeInsurancePolicies { get; set; }
        public DbSet<DebitCard> DebitCards { get; set; }
        public DbSet<RecurringPayment> RecurringPayments { get; set; }
        public DbSet<TravelInsurancePolicy> TravelInsurancePolicies { get; set; }
        public DbSet<GadgetInsurancePolicy> GadgetInsurancePolicies { get; set; }
        public DbSet<GlobalHealthInsuranceCard> GlobalHealthInsuranceCards { get; set; }
        public DbSet<VehicleWarranty> VehicleWarranties { get; set; }
        public DbSet<VehicleServicePlan> VehicleServicePlans { get; set; }
        public DbSet<VehicleFinanceAgreement> VehicleFinanceAgreements { get; set; }
        public DbSet<Address> Addresses { get; set; }
        public DbSet<VehicleService> VehicleServices { get; set; }
        public DbSet<Dentist> Dentists { get; set; }
        public DbSet<EyeTest> EyeTests { get; set; }
        public DbSet<GpPractice> GpPractices { get; set; }
        public DbSet<CreditCardAprHistoryRecord> CreditCardAprHistoryRecords { get; set; }
        public DbSet<CurrentAccountAprHistoryRecord> CurrentAccountAprHistoryRecords { get; set; }
        public DbSet<DirectDebit> DirectDebits { get; set; }
        public DbSet<PensionBalanceHistoryRecord> PensionBalanceHistoryRecords { get; set; }
        public DbSet<SavingsAccountAprHistoryRecord> SavingsAccountAprHistoryRecords { get; set; }
        public DbSet<SavingsAccountBalanceHistoryRecord> SavingsAccountBalanceHistoryRecords { get; set; }
        public DbSet<SavingsAccountPot> SavingsAccountPots { get; set; }
        public DbSet<SavingsAccountPotAprHistoryRecord> SavingsAccountPotAprHistoryRecords { get; set; }
        public DbSet<SavingsAccountPotBalanceHistoryRecord> SavingsAccountPotBalanceHistoryRecords { get; set; }
        public DbSet<StandingOrder> StandingOrders { get; set; }
        public DbSet<TaxableInterestPayment> TaxableInterestPayments { get; set; }
        public DbSet<VehicleInsuranceClaim> VehicleInsuranceClaims { get; set; }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            modelBuilder.Entity<User>().Property(x => x.Id).HasMaxLength(225);
            modelBuilder.Entity<IdentityRole>().Property(x => x.Id).HasMaxLength(225);
            modelBuilder.Entity<IdentityUserLogin<string>>().Property(x => x.ProviderKey).HasMaxLength(225);
            modelBuilder.Entity<IdentityUserLogin<string>>().Property(x => x.LoginProvider).HasMaxLength(225);
            modelBuilder.Entity<IdentityUserToken<string>>().Property(x => x.Name).HasMaxLength(112);
            modelBuilder.Entity<IdentityUserToken<string>>().Property(x => x.LoginProvider).HasMaxLength(112);

            // Configure one-to-one relationships with Reminder entity
            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.GeneralItem)
                .WithOne(gi => gi.Reminder)
                .HasForeignKey<Reminder>(r => r.GeneralItemId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.DrivingLicence)
                .WithMany(dl => dl.Reminders)
                .HasForeignKey(r => r.DrivingLicenceId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.Passport)
                .WithOne(p => p.Reminder)
                .HasForeignKey<Reminder>(r => r.PassportId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.VehicleTax)
                .WithOne(vt => vt.Reminder)
                .HasForeignKey<Reminder>(r => r.VehicleTaxId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.VehicleInsurancePolicy)
                .WithOne(vip => vip.Reminder)
                .HasForeignKey<Reminder>(r => r.VehicleInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.VehicleMot)
                .WithOne(vm => vm.Reminder)
                .HasForeignKey<Reminder>(r => r.VehicleMotId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.Vehicle)
                .WithOne(v => v.Reminder)
                .HasForeignKey<Reminder>(r => r.VehicleId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.VehicleBreakdownPolicy)
                .WithOne(vbp => vbp.Reminder)
                .HasForeignKey<Reminder>(r => r.VehicleBreakdownPolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.AddressInsurancePolicy)
                .WithOne(aip => aip.Reminder)
                .HasForeignKey<Reminder>(r => r.AddressInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.Tenancy)
                .WithOne(t => t.Reminder)
                .HasForeignKey<Reminder>(r => r.TenancyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.UtilityBill)
                .WithMany(ub => ub.Reminders)
                .HasForeignKey(r => r.UtilityBillId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.Mortgage)
                .WithMany(m => m.Reminders)
                .HasForeignKey(r => r.MortgageId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.CreditCard)
                .WithMany(cc => cc.Reminders)
                .HasForeignKey(r => r.CreditCardId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.Loan)
                .WithOne(l => l.Reminder)
                .HasForeignKey<Reminder>(r => r.LoanId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.LifeInsurancePolicy)
                .WithOne(lip => lip.Reminder)
                .HasForeignKey<Reminder>(r => r.LifeInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.DebitCard)
                .WithOne(dc => dc.Reminder)
                .HasForeignKey<Reminder>(r => r.DebitCardId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.RecurringPayment)
                .WithOne(rp => rp.Reminder)
                .HasForeignKey<Reminder>(r => r.RecurringPaymentId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.TravelInsurancePolicy)
                .WithOne(tip => tip.Reminder)
                .HasForeignKey<Reminder>(r => r.TravelInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.GadgetInsurancePolicy)
                .WithOne(gip => gip.Reminder)
                .HasForeignKey<Reminder>(r => r.GadgetInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.GlobalHealthInsuranceCard)
                .WithOne(ghic => ghic.Reminder)
                .HasForeignKey<Reminder>(r => r.GlobalHealthInsuranceCardId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.VehicleWarranty)
                .WithOne(vw => vw.Reminder)
                .HasForeignKey<Reminder>(r => r.VehicleWarrantyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.VehicleServicePlan)
                .WithOne(vsp => vsp.Reminder)
                .HasForeignKey<Reminder>(r => r.VehicleServicePlanId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.VehicleFinanceAgreement)
                .WithOne(vfa => vfa.Reminder)
                .HasForeignKey<Reminder>(r => r.VehicleFinanceAgreementId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.EyeTest)
                .WithOne(et => et.Reminder)
                .HasForeignKey<Reminder>(r => r.EyeTestId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.SavingsAccount)
                .WithOne(sa => sa.Reminder)
                .HasForeignKey<Reminder>(r => r.SavingsAccountId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Reminder>()
                .HasOne(r => r.SavingsAccountPot)
                .WithOne(sap => sap.Reminder)
                .HasForeignKey<Reminder>(r => r.SavingsAccountPotId)
                .OnDelete(DeleteBehavior.NoAction);

            // Configure DirectDebit relationships (NO ACTION)
            modelBuilder.Entity<DirectDebit>()
                .HasOne(dd => dd.CurrentAccount)
                .WithMany(ca => ca.DirectDebits)
                .HasForeignKey(dd => dd.CurrentAccountId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<DirectDebit>()
                .HasOne(dd => dd.GeneralItem)
                .WithOne(gi => gi.DirectDebit)
                .HasForeignKey<DirectDebit>(dd => dd.GeneralItemId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<DirectDebit>()
                .HasOne(dd => dd.UtilityBill)
                .WithOne(ub => ub.DirectDebit)
                .HasForeignKey<DirectDebit>(dd => dd.UtilityBillId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<DirectDebit>()
                .HasOne(dd => dd.Mortgage)
                .WithOne(m => m.DirectDebit)
                .HasForeignKey<DirectDebit>(dd => dd.MortgageId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<DirectDebit>()
                .HasOne(dd => dd.AddressInsurancePolicy)
                .WithOne(aip => aip.DirectDebit)
                .HasForeignKey<DirectDebit>(dd => dd.AddressInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<DirectDebit>()
                .HasOne(dd => dd.Tenancy)
                .WithOne(t => t.DirectDebit)
                .HasForeignKey<DirectDebit>(dd => dd.TenancyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<DirectDebit>()
                .HasOne(dd => dd.LifeInsurancePolicy)
                .WithOne(lip => lip.DirectDebit)
                .HasForeignKey<DirectDebit>(dd => dd.LifeInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<DirectDebit>()
                .HasOne(dd => dd.VehicleFinanceAgreement)
                .WithOne(vfa => vfa.DirectDebit)
                .HasForeignKey<DirectDebit>(dd => dd.VehicleFinanceAgreementId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<DirectDebit>()
                .HasOne(dd => dd.VehicleTax)
                .WithOne(vt => vt.DirectDebit)
                .HasForeignKey<DirectDebit>(dd => dd.VehicleTaxId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<DirectDebit>()
                .HasOne(dd => dd.Loan)
                .WithOne(l => l.DirectDebit)
                .HasForeignKey<DirectDebit>(dd => dd.LoanId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<DirectDebit>()
                .HasOne(dd => dd.TravelInsurancePolicy)
                .WithOne(tip => tip.DirectDebit)
                .HasForeignKey<DirectDebit>(dd => dd.TravelInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<DirectDebit>()
                .HasOne(dd => dd.GadgetInsurancePolicy)
                .WithOne(gip => gip.DirectDebit)
                .HasForeignKey<DirectDebit>(dd => dd.GadgetInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<DirectDebit>()
                .HasOne(dd => dd.VehicleInsurancePolicy)
                .WithOne(vip => vip.DirectDebit)
                .HasForeignKey<DirectDebit>(dd => dd.VehicleInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            // Configure StandingOrder relationships (NO ACTION)
            modelBuilder.Entity<StandingOrder>()
                .HasOne(so => so.CurrentAccount)
                .WithMany(ca => ca.StandingOrders)
                .HasForeignKey(so => so.CurrentAccountId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<StandingOrder>()
                .HasOne(so => so.GeneralItem)
                .WithOne(gi => gi.StandingOrder)
                .HasForeignKey<StandingOrder>(so => so.GeneralItemId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<StandingOrder>()
                .HasOne(so => so.UtilityBill)
                .WithOne(ub => ub.StandingOrder)
                .HasForeignKey<StandingOrder>(so => so.UtilityBillId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<StandingOrder>()
                .HasOne(so => so.VehicleTax)
                .WithOne(vt => vt.StandingOrder)
                .HasForeignKey<StandingOrder>(so => so.VehicleTaxId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<StandingOrder>()
                .HasOne(so => so.VehicleFinanceAgreement)
                .WithOne(vfa => vfa.StandingOrder)
                .HasForeignKey<StandingOrder>(so => so.VehicleFinanceAgreementId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<StandingOrder>()
                .HasOne(so => so.Loan)
                .WithOne(l => l.StandingOrder)
                .HasForeignKey<StandingOrder>(so => so.LoanId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<StandingOrder>()
                .HasOne(so => so.Mortgage)
                .WithOne(m => m.StandingOrder)
                .HasForeignKey<StandingOrder>(so => so.MortgageId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<StandingOrder>()
                .HasOne(so => so.AddressInsurancePolicy)
                .WithOne(aip => aip.StandingOrder)
                .HasForeignKey<StandingOrder>(so => so.AddressInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<StandingOrder>()
                .HasOne(so => so.Tenancy)
                .WithOne(t => t.StandingOrder)
                .HasForeignKey<StandingOrder>(so => so.TenancyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<StandingOrder>()
                .HasOne(so => so.LifeInsurancePolicy)
                .WithOne(lip => lip.StandingOrder)
                .HasForeignKey<StandingOrder>(so => so.LifeInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<StandingOrder>()
                .HasOne(so => so.TravelInsurancePolicy)
                .WithOne(tip => tip.StandingOrder)
                .HasForeignKey<StandingOrder>(so => so.TravelInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<StandingOrder>()
                .HasOne(so => so.GadgetInsurancePolicy)
                .WithOne(gip => gip.StandingOrder)
                .HasForeignKey<StandingOrder>(so => so.GadgetInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<StandingOrder>()
                .HasOne(so => so.VehicleInsurancePolicy)
                .WithOne(vip => vip.StandingOrder)
                .HasForeignKey<StandingOrder>(so => so.VehicleInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            // Configure Vehicle relationships (NO ACTION)
            modelBuilder.Entity<VehicleInsurancePolicy>()
                .HasOne(vip => vip.Vehicle)
                .WithMany(v => v.VehicleInsurancePolicies)
                .HasForeignKey(vip => vip.VehicleId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VehicleBreakdownPolicy>()
                .HasOne(vbp => vbp.Vehicle)
                .WithMany(v => v.VehicleBreakdownPolicies)
                .HasForeignKey(vbp => vbp.VehicleId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VehicleMot>()
                .HasOne(vm => vm.Vehicle)
                .WithMany(v => v.VehicleMots)
                .HasForeignKey(vm => vm.VehicleId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VehicleService>()
                .HasOne(vs => vs.Vehicle)
                .WithMany(v => v.VehicleServices)
                .HasForeignKey(vs => vs.VehicleId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VehicleTax>()
                .HasOne(vt => vt.Vehicle)
                .WithMany(v => v.VehicleTaxes)
                .HasForeignKey(vt => vt.VehicleId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VehicleWarranty>()
                .HasOne(vw => vw.Vehicle)
                .WithMany(v => v.VehicleWarranties)
                .HasForeignKey(vw => vw.VehicleId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VehicleServicePlan>()
                .HasOne(vsp => vsp.Vehicle)
                .WithMany(v => v.VehicleServicePlans)
                .HasForeignKey(vsp => vsp.VehicleId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VehicleFinanceAgreement>()
                .HasOne(vfa => vfa.Vehicle)
                .WithMany(v => v.VehicleFinanceAgreements)
                .HasForeignKey(vfa => vfa.VehicleId)
                .OnDelete(DeleteBehavior.NoAction);

            // Configure Address relationships (NO ACTION)
            modelBuilder.Entity<Vehicle>()
                .HasOne(v => v.Address)
                .WithMany(a => a.Vehicles)
                .HasForeignKey(v => v.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<GeneralItem>()
                .HasOne(gi => gi.Address)
                .WithMany(a => a.GeneralItems)
                .HasForeignKey(gi => gi.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<DrivingLicence>()
                .HasOne(dl => dl.Address)
                .WithMany(a => a.DrivingLicences)
                .HasForeignKey(dl => dl.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<GpPractice>()
                .HasOne(gp => gp.Address)
                .WithMany(a => a.GpPractices)
                .HasForeignKey(gp => gp.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Dentist>()
                .HasOne(d => d.Address)
                .WithMany(a => a.Dentists)
                .HasForeignKey(d => d.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<CurrentAccount>()
                .HasOne(ca => ca.Address)
                .WithMany(a => a.CurrentAccounts)
                .HasForeignKey(ca => ca.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            // Configure DebitCard → CurrentAccount relationship (1:1)
            modelBuilder.Entity<DebitCard>()
                .HasOne(dc => dc.CurrentAccount)
                .WithOne(ca => ca.DebitCard)
                .HasForeignKey<DebitCard>(dc => dc.CurrentAccountId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<CreditCard>()
                .HasOne(cc => cc.Address)
                .WithMany(a => a.CreditCards)
                .HasForeignKey(cc => cc.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<SavingsAccount>()
                .HasOne(sa => sa.Address)
                .WithMany(a => a.SavingsAccounts)
                .HasForeignKey(sa => sa.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Loan>()
                .HasOne(l => l.Address)
                .WithMany(a => a.Loans)
                .HasForeignKey(l => l.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Mortgage>()
                .HasOne(m => m.Address)
                .WithMany(a => a.Mortgages)
                .HasForeignKey(m => m.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Pension>()
                .HasOne(p => p.Address)
                .WithMany(a => a.Pensions)
                .HasForeignKey(p => p.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<TravelInsurancePolicy>()
                .HasOne(tip => tip.Address)
                .WithMany(a => a.TravelInsurancePolicies)
                .HasForeignKey(tip => tip.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<GadgetInsurancePolicy>()
                .HasOne(gip => gip.Address)
                .WithMany(a => a.GadgetInsurancePolicies)
                .HasForeignKey(gip => gip.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VehicleInsurancePolicy>()
                .HasOne(vip => vip.Address)
                .WithMany(a => a.VehicleInsurancePolicies)
                .HasForeignKey(vip => vip.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VehicleBreakdownPolicy>()
                .HasOne(vbp => vbp.Address)
                .WithMany(a => a.VehicleBreakdownPolicies)
                .HasForeignKey(vbp => vbp.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<AddressInsurancePolicy>()
                .HasOne(aip => aip.Address)
                .WithMany(a => a.AddressInsurancePolicies)
                .HasForeignKey(aip => aip.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VehicleFinanceAgreement>()
                .HasOne(vfa => vfa.Address)
                .WithMany(a => a.VehicleFinanceAgreements)
                .HasForeignKey(vfa => vfa.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Tenancy>()
                .HasOne(t => t.Address)
                .WithMany(a => a.Tenancies)
                .HasForeignKey(t => t.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<UtilityBill>()
                .HasOne(ub => ub.Address)
                .WithMany(a => a.UtilityBills)
                .HasForeignKey(ub => ub.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<LifeInsurancePolicy>()
                .HasOne(lip => lip.Address)
                .WithMany(a => a.LifeInsurancePolicies)
                .HasForeignKey(lip => lip.AddressId)
                .OnDelete(DeleteBehavior.NoAction);

            // Configure financial account relationships (NO ACTION)
            modelBuilder.Entity<CreditCardAprHistoryRecord>()
                .HasOne(cahr => cahr.CreditCard)
                .WithMany(cc => cc.CreditCardAprHistoryRecords)
                .HasForeignKey(cahr => cahr.CreditCardId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<CurrentAccountAprHistoryRecord>()
                .HasOne(cahr => cahr.CurrentAccount)
                .WithMany(ca => ca.CurrentAccountAprHistoryRecords)
                .HasForeignKey(cahr => cahr.CurrentAccountId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<PensionBalanceHistoryRecord>()
                .HasOne(pbhr => pbhr.Pension)
                .WithMany(p => p.PensionBalanceHistoryRecords)
                .HasForeignKey(pbhr => pbhr.PensionId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<SavingsAccountAprHistoryRecord>()
                .HasOne(sahr => sahr.SavingsAccount)
                .WithMany(sa => sa.SavingsAccountAprHistoryRecords)
                .HasForeignKey(sahr => sahr.SavingsAccountId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<SavingsAccountBalanceHistoryRecord>()
                .HasOne(sabhr => sabhr.SavingsAccount)
                .WithMany(sa => sa.SavingsAccountBalanceHistoryRecords)
                .HasForeignKey(sabhr => sabhr.SavingsAccountId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<SavingsAccountPot>()
                .HasOne(sap => sap.SavingsAccount)
                .WithMany(sa => sa.SavingsAccountPots)
                .HasForeignKey(sap => sap.SavingsAccountId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<SavingsAccountPotAprHistoryRecord>()
                .HasOne(sapahr => sapahr.SavingsAccountPot)
                .WithMany(sap => sap.SavingsAccountPotAprHistoryRecords)
                .HasForeignKey(sapahr => sapahr.SavingsAccountPotId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<SavingsAccountPotBalanceHistoryRecord>()
                .HasOne(sapbhr => sapbhr.SavingsAccountPot)
                .WithMany(sap => sap.SavingsAccountPotBalanceHistoryRecords)
                .HasForeignKey(sapbhr => sapbhr.SavingsAccountPotId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<TaxableInterestPayment>()
                .HasOne(tip => tip.CurrentAccount)
                .WithMany(ca => ca.TaxableInterestPayments)
                .HasForeignKey(tip => tip.CurrentAccountId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<TaxableInterestPayment>()
                .HasOne(tip => tip.SavingsAccount)
                .WithMany(sa => sa.TaxableInterestPayments)
                .HasForeignKey(tip => tip.SavingsAccountId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<TaxableInterestPayment>()
                .HasOne(tip => tip.SavingsAccountPot)
                .WithMany(sap => sap.TaxableInterestPayments)
                .HasForeignKey(tip => tip.SavingsAccountPotId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VehicleInsuranceClaim>()
                .HasOne(vic => vic.VehicleInsurancePolicy)
                .WithMany(vip => vip.VehicleInsuranceClaims)
                .HasForeignKey(vic => vic.VehicleInsurancePolicyId)
                .OnDelete(DeleteBehavior.NoAction);

            // Configure RecurringPayment relationships (NO ACTION)
            modelBuilder.Entity<RecurringPayment>()
                .HasOne(rp => rp.CreditCard)
                .WithMany(cc => cc.RecurringPayments)
                .HasForeignKey(rp => rp.CreditCardId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<RecurringPayment>()
                .HasOne(rp => rp.DebitCard)
                .WithMany(dc => dc.RecurringPayments)
                .HasForeignKey(rp => rp.DebitCardId)
                .OnDelete(DeleteBehavior.NoAction);
        }
    }
}
