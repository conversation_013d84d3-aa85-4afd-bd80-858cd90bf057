using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.Reminders
{
    public class IndexModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public IndexModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public IList<Reminder> Reminders { get; set; } = default!;

        public IList<Reminder> ExpiredReminders { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();
            
            Reminders = await _context.Reminders
                .AsNoTracking()
                .Where(r => r.UserId == currentUser.Id)
                .Where(r => r.ReminderDate >= DateOnly.FromDateTime(DateTime.UtcNow))
                .Include(r => r.GeneralItem)
                .Include(r => r.DrivingLicence)
                .Include(r => r.Passport)
                .Include(r => r.VehicleTax)
                .Include(r => r.VehicleInsurancePolicy)
                .Include(r => r.VehicleMot)
                .Include(r => r.Vehicle)
                .Include(r => r.VehicleBreakdownPolicy)
                .Include(r => r.AddressInsurancePolicy)
                .Include(r => r.Tenancy)
                .Include(r => r.UtilityBill)
                .Include(r => r.Mortgage)
                .Include(r => r.CreditCard)
                .Include(r => r.Loan)
                .Include(r => r.LifeInsurancePolicy)
                .Include(r => r.DebitCard)
                .Include(r => r.RecurringPayment)
                .Include(r => r.TravelInsurancePolicy)
                .Include(r => r.GadgetInsurancePolicy)
                .Include(r => r.GlobalHealthInsuranceCard)
                .Include(r => r.EyeTest)
                .Include(r => r.VehicleWarranty)
                .Include(r => r.VehicleServicePlan)
                .Include(r => r.VehicleFinanceAgreement)
                .Include(r => r.SavingsAccount)
                .Include(r => r.SavingsAccountPot)
                .OrderBy(r => r.ReminderDate)
                .ToListAsync();

            ExpiredReminders = await _context.Reminders
                .AsNoTracking()
                .Where(r => r.UserId == currentUser.Id)
                .Where(r => r.ReminderDate < DateOnly.FromDateTime(DateTime.UtcNow))
                .Include(r => r.GeneralItem)
                .Include(r => r.DrivingLicence)
                .Include(r => r.Passport)
                .Include(r => r.VehicleTax)
                .Include(r => r.VehicleInsurancePolicy)
                .Include(r => r.VehicleMot)
                .Include(r => r.Vehicle)
                .Include(r => r.VehicleBreakdownPolicy)
                .Include(r => r.AddressInsurancePolicy)
                .Include(r => r.Tenancy)
                .Include(r => r.UtilityBill)
                .Include(r => r.Mortgage)
                .Include(r => r.CreditCard)
                .Include(r => r.Loan)
                .Include(r => r.LifeInsurancePolicy)
                .Include(r => r.DebitCard)
                .Include(r => r.RecurringPayment)
                .Include(r => r.TravelInsurancePolicy)
                .Include(r => r.GadgetInsurancePolicy)
                .Include(r => r.GlobalHealthInsuranceCard)
                .Include(r => r.EyeTest)
                .Include(r => r.VehicleWarranty)
                .Include(r => r.VehicleServicePlan)
                .Include(r => r.VehicleFinanceAgreement)
                .Include(r => r.SavingsAccount)
                .Include(r => r.SavingsAccountPot)
                .OrderBy(r => r.ReminderDate)
                .ToListAsync();


            return Page();
        }

        public string? GetRelatedItemLink(Reminder reminder)
        {
            if (reminder.GeneralItem != null) return $"/generalitems/details/{reminder.GeneralItem.Id}";
            if (reminder.DrivingLicence != null) return $"/drivinglicences/details/{reminder.DrivingLicence.Id}";
            if (reminder.Passport != null) return $"/passports/details?id={reminder.Passport.Id}";
            if (reminder.VehicleTax != null) return $"/vehicletaxes/details/{reminder.VehicleTax.Id}";
            if (reminder.VehicleInsurancePolicy != null) return $"/vehicleinsurancepolicies/details/{reminder.VehicleInsurancePolicy.Id}";
            if (reminder.VehicleMot != null) return $"/vehicles/details/{reminder.Vehicle.Id}";
            if (reminder.Vehicle != null) return $"/vehicles/details/{reminder.Vehicle.Id}";
            if (reminder.VehicleBreakdownPolicy != null) return $"/vehiclebreakdownpolicies/details/{reminder.VehicleBreakdownPolicy.Id}";
            if (reminder.AddressInsurancePolicy != null) return $"/addressinsurancepolicies/details/{reminder.AddressInsurancePolicy.Id}";
            if (reminder.Tenancy != null) return $"/tenancies/details/{reminder.Tenancy.Id}";
            if (reminder.UtilityBill != null) return $"/utilitybills/details/{reminder.UtilityBill.Id}";
            if (reminder.Mortgage != null) return $"/mortgages/details/{reminder.Mortgage.Id}";
            if (reminder.CreditCard != null) return $"/creditcards/details/{reminder.CreditCard.Id}";
            if (reminder.Loan != null) return $"/loans/details/{reminder.Loan.Id}";
            if (reminder.LifeInsurancePolicy != null) return $"/lifeinsurancepolicies/details/{reminder.LifeInsurancePolicy.Id}";
            if (reminder.DebitCard != null) return $"/debitcards/details/{reminder.DebitCard.Id}";
            if (reminder.RecurringPayment != null) return $"/recurringpayments/details/{reminder.RecurringPayment.Id}";
            if (reminder.TravelInsurancePolicy != null) return $"/travelinsurancepolicies/details/{reminder.TravelInsurancePolicy.Id}";
            if (reminder.GadgetInsurancePolicy != null) return $"/gadgetinsurancepolicies/details/{reminder.GadgetInsurancePolicy.Id}";
            if (reminder.GlobalHealthInsuranceCard != null) return $"/globalhealthinsurancecards/details/{reminder.GlobalHealthInsuranceCard.Id}";
            if (reminder.EyeTest != null) return $"/eyetests/details/{reminder.EyeTest.Id}";
            if (reminder.VehicleWarranty != null) return $"/vehiclewarranties/details/{reminder.VehicleWarranty.Id}";
            if (reminder.VehicleServicePlan != null) return $"/vehicleserviceplans/details/{reminder.VehicleServicePlan.Id}";
            if (reminder.VehicleFinanceAgreement != null) return $"/vehiclefinanceagreements/details/{reminder.VehicleFinanceAgreement.Id}";
            if (reminder.SavingsAccount != null) return $"/savingsaccounts/details/{reminder.SavingsAccount.Id}";
            if (reminder.SavingsAccountPot != null) return $"/savingsaccountpots/details/{reminder.SavingsAccountPot.Id}";

            return null;
        }
    }
}
