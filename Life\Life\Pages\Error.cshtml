﻿@page
@model ErrorModel
@{
    ViewData["Title"] = "Error";
}

<div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
    <div class="row justify-content-center w-100">
        <div class="col-md-6 col-lg-4">
            <div class="text-center">
                <a href="~/" class="d-block mb-4">
                    <img src="~/logo.png" alt="Life" style="height: 60px;">
                </a>

                <h1 class="text-danger">Error @(Model.Code?.ToString() ?? "")</h1>
                
                @if (Model.Code.HasValue)
                {
                    <h2 class="text-danger">
                        @switch (Model.Code.Value)
                        {
                            case 404:
                                @:Page Not Found
                                break;
                            case 500:
                                @:Internal Server Error
                                break;
                            case 403:
                                @:Access Forbidden
                                break;
                            default:
                                @:An error occurred while processing your request.
                                break;
                        }
                    </h2>
                }
                else
                {
                    <h2 class="text-danger">An error occurred while processing your request.</h2>
                }
                
                <div class="mt-4">
                    <a href="~/" class="btn btn-primary">Home</a>
                </div>
            </div>
        </div>
    </div>
</div>