using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class VehicleFinanceAgreement
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        public int VehicleId { get; set; }
        
        [ForeignKey("VehicleId")]
        public Vehicle Vehicle { get; set; } = null!;
        
        public int? AddressId { get; set; }
        
        [ForeignKey("AddressId")]
        public Address? Address { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string Provider { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string AgreementNumber { get; set; } = string.Empty;
        
        [Required]
        [StringLength(20)]
        public string FinanceType { get; set; } = string.Empty;
        
        public DateOnly? StartDate { get; set; }

        public DateOnly? ExpectedEndDate { get; set; }

        public int? LoanTermMonths { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? LoanAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? TotalRepayableAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? RemainingBalance { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? PaymentAmount { get; set; }

        [StringLength(4)]
        public string? PaymentDayOfMonth { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? Deposit { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? VehiclePrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? Apr { get; set; }

        [Required]
        public bool Settled { get; set; }

        public DateOnly? SettledDate { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public DirectDebit? DirectDebit { get; set; }
        public StandingOrder? StandingOrder { get; set; }
        public Reminder? Reminder { get; set; }
    }
}
