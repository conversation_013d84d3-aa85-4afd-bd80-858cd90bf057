using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class Tenancy
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        public int AddressId { get; set; }
        
        [ForeignKey("AddressId")]
        public Address Address { get; set; } = null!;
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? LandlordName { get; set; }
        
        [StringLength(20)]
        public string? LandlordPhoneNumber { get; set; }
        
        [StringLength(50)]
        public string? AgentName { get; set; }
        
        [StringLength(20)]
        public string? AgentPhoneNumber { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? RentAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Deposit { get; set; }
        
        [StringLength(4)]
        public string? RentDayOfMonth { get; set; }
        
        [Required]
        public DateOnly StartDate { get; set; }

        public DateOnly? EndDate { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public DirectDebit? DirectDebit { get; set; }
        public StandingOrder? StandingOrder { get; set; }
        public Reminder? Reminder { get; set; }
    }
}
