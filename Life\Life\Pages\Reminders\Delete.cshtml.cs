using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.Reminders
{
    public class DeleteModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DeleteModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public int ReminderId { get; set; }

        public string Name { get; set; } = string.Empty;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var reminder = await _context.Reminders
                .Where(r => r.Id == id && r.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (reminder == null)
            {
                return NotFound();
            }

            // Only allow deletion of custom reminders
            if (reminder.IsAutomatic)
            {
                return NotFound();
            }

            ReminderId = reminder.Id;
            Name = reminder.Name;
            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var reminder = await _context.Reminders
                .Where(r => r.Id == id && r.UserId == currentUser.Id && !r.IsAutomatic)
                .FirstOrDefaultAsync();

            if (reminder == null)
            {
                return NotFound();
            }

            _context.Reminders.Remove(reminder);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Reminder deleted successfully";

            return RedirectToPage("./Index");
        }
    }
}
