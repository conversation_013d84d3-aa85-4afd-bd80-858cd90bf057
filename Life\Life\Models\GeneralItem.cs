using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class GeneralItem
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        public int? AddressId { get; set; }
        
        [ForeignKey("AddressId")]
        public Address? Address { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(2000)]
        public string? Details { get; set; }
        
        public DateOnly? StartDate { get; set; }

        public DateOnly? ExpiryDate { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties - 1:1 relationships only
        public DirectDebit? DirectDebit { get; set; }
        public StandingOrder? StandingOrder { get; set; }
        public Reminder? Reminder { get; set; }
    }
}
