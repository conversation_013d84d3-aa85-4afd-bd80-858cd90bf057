﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Life.Migrations
{
    /// <inheritdoc />
    public partial class AddTyrePressureFieldsToVehicle : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "HighLoadFrontPressure",
                table: "Vehicles",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "HighLoadRearPressure",
                table: "Vehicles",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LowLoadFrontPressure",
                table: "Vehicles",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LowLoadRearPressure",
                table: "Vehicles",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UnitOfPressure",
                table: "Vehicles",
                type: "nvarchar(3)",
                maxLength: 3,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HighLoadFrontPressure",
                table: "Vehicles");

            migrationBuilder.DropColumn(
                name: "HighLoadRearPressure",
                table: "Vehicles");

            migrationBuilder.DropColumn(
                name: "LowLoadFrontPressure",
                table: "Vehicles");

            migrationBuilder.DropColumn(
                name: "LowLoadRearPressure",
                table: "Vehicles");

            migrationBuilder.DropColumn(
                name: "UnitOfPressure",
                table: "Vehicles");
        }
    }
}
