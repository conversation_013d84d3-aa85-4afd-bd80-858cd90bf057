using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class DrivingLicence
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        public int? AddressId { get; set; }
        
        [ForeignKey("AddressId")]
        public Address? Address { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        public DateOnly StartDate { get; set; }

        [Required]
        public DateOnly ExpiryDate { get; set; }

        [Required]
        public DateOnly PhotocardStartDate { get; set; }

        [Required]
        public DateOnly PhotocardExpiryDate { get; set; }
        
        [StringLength(16)]
        public string? LicenceNumber { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public ICollection<Reminder> Reminders { get; set; } = new List<Reminder>();
    }
}
