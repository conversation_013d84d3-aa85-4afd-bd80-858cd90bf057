﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Life.Migrations
{
    /// <inheritdoc />
    public partial class AddAllEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Addresses",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    HouseFlatNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    StreetLineOne = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    StreetLineTwo = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    City = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Postcode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    MoveInDate = table.Column<DateOnly>(type: "date", nullable: true),
                    MoveOutDate = table.Column<DateOnly>(type: "date", nullable: true),
                    PurchaseDate = table.Column<DateOnly>(type: "date", nullable: true),
                    PurchasePrice = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    SoldDate = table.Column<DateOnly>(type: "date", nullable: true),
                    SoldPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Addresses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Addresses_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EyeTests",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    TestDate = table.Column<DateOnly>(type: "date", nullable: false),
                    NextTestDate = table.Column<DateOnly>(type: "date", nullable: true),
                    RightSPH = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    RightCYL = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    RightAXIS = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    LeftSPH = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    LeftCYL = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    LeftAXIS = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Cost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EyeTests", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EyeTests_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "GlobalHealthInsuranceCards",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: false),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: false),
                    PersonalIdNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CardIdNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GlobalHealthInsuranceCards", x => x.Id);
                    table.ForeignKey(
                        name: "FK_GlobalHealthInsuranceCards_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Passports",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: false),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: false),
                    PassportNumber = table.Column<int>(type: "int", nullable: false),
                    Cost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Size = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Passports", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Passports_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AddressInsurancePolicies",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PolicyNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    PolicyCost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Type = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: false),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: false),
                    AutoRenewal = table.Column<bool>(type: "bit", nullable: false),
                    ClaimsPhoneNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    BuildingsExcess = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    ContentsExcess = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    BuildingsCover = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    ContentsCover = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentMethod = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AddressInsurancePolicies", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AddressInsurancePolicies_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AddressInsurancePolicies_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CreditCards",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: false),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: true),
                    PromotionalPeriodEndDate = table.Column<DateOnly>(type: "date", nullable: true),
                    Apr = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    CreditLimit = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentDayOfMonth = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CreditCards", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CreditCards_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CreditCards_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CurrentAccounts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    MonthlyFee = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    MinimumMonthlyFunding = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    OverdraftLimit = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    OverdraftApr = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    InterestApr = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    DateOpened = table.Column<DateOnly>(type: "date", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CurrentAccounts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CurrentAccounts_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CurrentAccounts_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Dentists",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    StreetLineOne = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    StreetLineTwo = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    City = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Postcode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    PhoneNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Website = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Dentists", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Dentists_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Dentists_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DrivingLicences",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: false),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: false),
                    PhotocardStartDate = table.Column<DateOnly>(type: "date", nullable: false),
                    PhotocardExpiryDate = table.Column<DateOnly>(type: "date", nullable: false),
                    LicenceNumber = table.Column<string>(type: "nvarchar(16)", maxLength: 16, nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DrivingLicences", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DrivingLicences_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DrivingLicences_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "GadgetInsurancePolicies",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PolicyNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    PolicyCost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    ClaimsPhoneNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: true),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: true),
                    AutoRenewal = table.Column<bool>(type: "bit", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GadgetInsurancePolicies", x => x.Id);
                    table.ForeignKey(
                        name: "FK_GadgetInsurancePolicies_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_GadgetInsurancePolicies_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "GeneralItems",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Details = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: true),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GeneralItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_GeneralItems_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_GeneralItems_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "GpPractices",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    StreetLineOne = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    StreetLineTwo = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    City = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Postcode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    PhoneNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Website = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GpPractices", x => x.Id);
                    table.ForeignKey(
                        name: "FK_GpPractices_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_GpPractices_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "LifeInsurancePolicies",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: false),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: false),
                    PolicyNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    MonthlyCost = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    InsuranceAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LifeInsurancePolicies", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LifeInsurancePolicies_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_LifeInsurancePolicies_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Loans",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Apr = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentDayOfMonth = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: true),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: true),
                    ExpectedEndDate = table.Column<DateOnly>(type: "date", nullable: true),
                    LoanTermMonths = table.Column<int>(type: "int", nullable: true),
                    LoanAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    TotalRepayableAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    RemainingBalance = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Settled = table.Column<bool>(type: "bit", nullable: false),
                    SettledDate = table.Column<DateOnly>(type: "date", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Loans", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Loans_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Loans_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Mortgages",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ArrangementFee = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    BookingFee = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    RemittanceFee = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    ValuationFee = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    FixedTermApr = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    StandardVariableRate = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    TotalTermMonths = table.Column<int>(type: "int", nullable: true),
                    FixedTermMonths = table.Column<int>(type: "int", nullable: true),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: true),
                    FixedTermEndDate = table.Column<DateOnly>(type: "date", nullable: true),
                    EndDate = table.Column<DateOnly>(type: "date", nullable: true),
                    PaymentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentDayOfMonth = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: true),
                    Type = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    EarlyRepaymentCharge = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    RemainingBalance = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Active = table.Column<bool>(type: "bit", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Mortgages", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Mortgages_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Mortgages_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Pensions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Balance = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    MonthlyContributionAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    MonthlyEmployerContributionAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    ContributionFeePercentage = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    AnnualChargePercentage = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    DateOpened = table.Column<DateOnly>(type: "date", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Pensions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Pensions_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Pensions_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SavingsAccounts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    AccountType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    FixedTermMonths = table.Column<int>(type: "int", nullable: true),
                    FixedTermEndDate = table.Column<DateOnly>(type: "date", nullable: true),
                    AccountBalance = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Apr = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    InterestFrequency = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    DateOpened = table.Column<DateOnly>(type: "date", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SavingsAccounts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SavingsAccounts_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SavingsAccounts_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Tenancies",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    LandlordName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    LandlordPhoneNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    AgentName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    AgentPhoneNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    RentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Deposit = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    RentDayOfMonth = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: true),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: false),
                    EndDate = table.Column<DateOnly>(type: "date", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Tenancies", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Tenancies_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Tenancies_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TravelInsurancePolicies",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PolicyNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    PolicyCost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    ClaimsPhoneNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: true),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: true),
                    AutoRenewal = table.Column<bool>(type: "bit", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TravelInsurancePolicies", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TravelInsurancePolicies_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TravelInsurancePolicies_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UtilityBills",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    AccountNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    PaymentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentFrequency = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    PaymentDayOfMonth = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: true),
                    PaymentMonth = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    PaymentMethod = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ContractStartDate = table.Column<DateOnly>(type: "date", nullable: true),
                    ContractEndDate = table.Column<DateOnly>(type: "date", nullable: true),
                    TariffStartDate = table.Column<DateOnly>(type: "date", nullable: true),
                    TariffEndDate = table.Column<DateOnly>(type: "date", nullable: true),
                    GasPencePerDay = table.Column<decimal>(type: "decimal(18,3)", nullable: true),
                    GasPencePerKWh = table.Column<decimal>(type: "decimal(18,3)", nullable: true),
                    ElectricityPencePerDay = table.Column<decimal>(type: "decimal(18,3)", nullable: true),
                    ElectricityPencePerKWh = table.Column<decimal>(type: "decimal(18,3)", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UtilityBills", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UtilityBills_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_UtilityBills_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Vehicles",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Registration = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    Make = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Model = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Colour = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Year = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: true),
                    Owner = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    VinNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    DatePurchased = table.Column<DateOnly>(type: "date", nullable: true),
                    OwnershipType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    TyreSize = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    KerbWeight = table.Column<int>(type: "int", nullable: true),
                    NextServiceDue = table.Column<DateOnly>(type: "date", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Vehicles", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Vehicles_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Vehicles_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CreditCardAprHistoryRecords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    CreditCardId = table.Column<int>(type: "int", nullable: false),
                    Apr = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Date = table.Column<DateOnly>(type: "date", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CreditCardAprHistoryRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CreditCardAprHistoryRecords_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CreditCardAprHistoryRecords_CreditCards_CreditCardId",
                        column: x => x.CreditCardId,
                        principalTable: "CreditCards",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CurrentAccountAprHistoryRecords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    CurrentAccountId = table.Column<int>(type: "int", nullable: false),
                    Apr = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Date = table.Column<DateOnly>(type: "date", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CurrentAccountAprHistoryRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CurrentAccountAprHistoryRecords_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CurrentAccountAprHistoryRecords_CurrentAccounts_CurrentAccountId",
                        column: x => x.CurrentAccountId,
                        principalTable: "CurrentAccounts",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "DebitCards",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    CurrentAccountId = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: false),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DebitCards", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DebitCards_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DebitCards_CurrentAccounts_CurrentAccountId",
                        column: x => x.CurrentAccountId,
                        principalTable: "CurrentAccounts",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "PensionBalanceHistoryRecords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    PensionId = table.Column<int>(type: "int", nullable: false),
                    Balance = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Date = table.Column<DateOnly>(type: "date", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PensionBalanceHistoryRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PensionBalanceHistoryRecords_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PensionBalanceHistoryRecords_Pensions_PensionId",
                        column: x => x.PensionId,
                        principalTable: "Pensions",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SavingsAccountAprHistoryRecords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    SavingsAccountId = table.Column<int>(type: "int", nullable: false),
                    Apr = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Date = table.Column<DateOnly>(type: "date", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SavingsAccountAprHistoryRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SavingsAccountAprHistoryRecords_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SavingsAccountAprHistoryRecords_SavingsAccounts_SavingsAccountId",
                        column: x => x.SavingsAccountId,
                        principalTable: "SavingsAccounts",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SavingsAccountBalanceHistoryRecords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    SavingsAccountId = table.Column<int>(type: "int", nullable: false),
                    AccountBalance = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Date = table.Column<DateOnly>(type: "date", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SavingsAccountBalanceHistoryRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SavingsAccountBalanceHistoryRecords_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SavingsAccountBalanceHistoryRecords_SavingsAccounts_SavingsAccountId",
                        column: x => x.SavingsAccountId,
                        principalTable: "SavingsAccounts",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SavingsAccountPots",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    SavingsAccountId = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PotType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    FixedTermMonths = table.Column<int>(type: "int", nullable: true),
                    FixedTermEndDate = table.Column<DateOnly>(type: "date", nullable: true),
                    PotBalance = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Apr = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    InterestFrequency = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    DateOpened = table.Column<DateOnly>(type: "date", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SavingsAccountPots", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SavingsAccountPots_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SavingsAccountPots_SavingsAccounts_SavingsAccountId",
                        column: x => x.SavingsAccountId,
                        principalTable: "SavingsAccounts",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "VehicleBreakdownPolicies",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    VehicleId = table.Column<int>(type: "int", nullable: true),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PolicyNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    PolicyCost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: true),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: true),
                    AutoRenewal = table.Column<bool>(type: "bit", nullable: false),
                    PaymentMethod = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    UKPhoneNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    EUPhoneNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    NamedDriver = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleBreakdownPolicies", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VehicleBreakdownPolicies_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VehicleBreakdownPolicies_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VehicleBreakdownPolicies_Vehicles_VehicleId",
                        column: x => x.VehicleId,
                        principalTable: "Vehicles",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "VehicleFinanceAgreements",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    VehicleId = table.Column<int>(type: "int", nullable: false),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    AgreementNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    FinanceType = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: true),
                    ExpectedEndDate = table.Column<DateOnly>(type: "date", nullable: true),
                    LoanTermMonths = table.Column<int>(type: "int", nullable: true),
                    LoanAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    TotalRepayableAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    RemainingBalance = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentDayOfMonth = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: true),
                    Deposit = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    VehiclePrice = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Apr = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Settled = table.Column<bool>(type: "bit", nullable: false),
                    SettledDate = table.Column<DateOnly>(type: "date", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleFinanceAgreements", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VehicleFinanceAgreements_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VehicleFinanceAgreements_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VehicleFinanceAgreements_Vehicles_VehicleId",
                        column: x => x.VehicleId,
                        principalTable: "Vehicles",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "VehicleInsurancePolicies",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    VehicleId = table.Column<int>(type: "int", nullable: true),
                    AddressId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PolicyNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    PolicyCost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: false),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: false),
                    AutoRenewal = table.Column<bool>(type: "bit", nullable: false),
                    MainDriver = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    NamedDrivers = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ClaimsPhoneNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Excess = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    GlassRepairExcess = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    GlassReplacementExcess = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentMethod = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    AnnualMileage = table.Column<int>(type: "int", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleInsurancePolicies", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VehicleInsurancePolicies_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VehicleInsurancePolicies_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VehicleInsurancePolicies_Vehicles_VehicleId",
                        column: x => x.VehicleId,
                        principalTable: "Vehicles",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "VehicleMots",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    VehicleId = table.Column<int>(type: "int", nullable: false),
                    TestDate = table.Column<DateOnly>(type: "date", nullable: false),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: false),
                    TestCentre = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Cost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Mileage = table.Column<int>(type: "int", nullable: true),
                    Advisories = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    PaymentMethod = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleMots", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VehicleMots_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VehicleMots_Vehicles_VehicleId",
                        column: x => x.VehicleId,
                        principalTable: "Vehicles",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "VehicleServicePlans",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    VehicleId = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PlanNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: false),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: false),
                    Cost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentMethod = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleServicePlans", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VehicleServicePlans_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VehicleServicePlans_Vehicles_VehicleId",
                        column: x => x.VehicleId,
                        principalTable: "Vehicles",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "VehicleServices",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    VehicleId = table.Column<int>(type: "int", nullable: false),
                    ServiceDate = table.Column<DateOnly>(type: "date", nullable: false),
                    ServiceType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ServiceCentre = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Cost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Mileage = table.Column<int>(type: "int", nullable: true),
                    PaymentMethod = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleServices", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VehicleServices_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VehicleServices_Vehicles_VehicleId",
                        column: x => x.VehicleId,
                        principalTable: "Vehicles",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "VehicleTaxes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    VehicleId = table.Column<int>(type: "int", nullable: false),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: false),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: false),
                    Cost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentMethod = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleTaxes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VehicleTaxes_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VehicleTaxes_Vehicles_VehicleId",
                        column: x => x.VehicleId,
                        principalTable: "Vehicles",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "VehicleWarranties",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    VehicleId = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Provider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PlanNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: false),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: false),
                    Cost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentMethod = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleWarranties", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VehicleWarranties_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VehicleWarranties_Vehicles_VehicleId",
                        column: x => x.VehicleId,
                        principalTable: "Vehicles",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "RecurringPayments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    CreditCardId = table.Column<int>(type: "int", nullable: true),
                    DebitCardId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PaymentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentFrequency = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    PaymentDayOfMonth = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: true),
                    PaymentMonth = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    StartDate = table.Column<DateOnly>(type: "date", nullable: true),
                    NextPaymentDate = table.Column<DateOnly>(type: "date", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RecurringPayments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RecurringPayments_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RecurringPayments_CreditCards_CreditCardId",
                        column: x => x.CreditCardId,
                        principalTable: "CreditCards",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RecurringPayments_DebitCards_DebitCardId",
                        column: x => x.DebitCardId,
                        principalTable: "DebitCards",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SavingsAccountPotAprHistoryRecords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    SavingsAccountPotId = table.Column<int>(type: "int", nullable: false),
                    Apr = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Date = table.Column<DateOnly>(type: "date", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SavingsAccountPotAprHistoryRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SavingsAccountPotAprHistoryRecords_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SavingsAccountPotAprHistoryRecords_SavingsAccountPots_SavingsAccountPotId",
                        column: x => x.SavingsAccountPotId,
                        principalTable: "SavingsAccountPots",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SavingsAccountPotBalanceHistoryRecords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    SavingsAccountPotId = table.Column<int>(type: "int", nullable: false),
                    PotBalance = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Date = table.Column<DateOnly>(type: "date", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SavingsAccountPotBalanceHistoryRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SavingsAccountPotBalanceHistoryRecords_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SavingsAccountPotBalanceHistoryRecords_SavingsAccountPots_SavingsAccountPotId",
                        column: x => x.SavingsAccountPotId,
                        principalTable: "SavingsAccountPots",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "TaxableInterestPayments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    CurrentAccountId = table.Column<int>(type: "int", nullable: true),
                    SavingsAccountId = table.Column<int>(type: "int", nullable: true),
                    SavingsAccountPotId = table.Column<int>(type: "int", nullable: true),
                    InterestAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Date = table.Column<DateOnly>(type: "date", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxableInterestPayments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TaxableInterestPayments_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TaxableInterestPayments_CurrentAccounts_CurrentAccountId",
                        column: x => x.CurrentAccountId,
                        principalTable: "CurrentAccounts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TaxableInterestPayments_SavingsAccountPots_SavingsAccountPotId",
                        column: x => x.SavingsAccountPotId,
                        principalTable: "SavingsAccountPots",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TaxableInterestPayments_SavingsAccounts_SavingsAccountId",
                        column: x => x.SavingsAccountId,
                        principalTable: "SavingsAccounts",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "VehicleInsuranceClaims",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    VehicleInsurancePolicyId = table.Column<int>(type: "int", nullable: false),
                    ClaimDate = table.Column<DateOnly>(type: "date", nullable: false),
                    ClaimReference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ClaimDetails = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleInsuranceClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VehicleInsuranceClaims_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VehicleInsuranceClaims_VehicleInsurancePolicies_VehicleInsurancePolicyId",
                        column: x => x.VehicleInsurancePolicyId,
                        principalTable: "VehicleInsurancePolicies",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "DirectDebits",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    CurrentAccountId = table.Column<int>(type: "int", nullable: false),
                    GeneralItemId = table.Column<int>(type: "int", nullable: true),
                    VehicleTaxId = table.Column<int>(type: "int", nullable: true),
                    VehicleFinanceAgreementId = table.Column<int>(type: "int", nullable: true),
                    LoanId = table.Column<int>(type: "int", nullable: true),
                    MortgageId = table.Column<int>(type: "int", nullable: true),
                    LifeInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    TravelInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    GadgetInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    VehicleInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    AddressInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    TenancyId = table.Column<int>(type: "int", nullable: true),
                    UtilityBillId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PaymentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentFrequency = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    PaymentDayOfMonth = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: true),
                    PaymentMonth = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DirectDebits", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DirectDebits_AddressInsurancePolicies_AddressInsurancePolicyId",
                        column: x => x.AddressInsurancePolicyId,
                        principalTable: "AddressInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DirectDebits_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DirectDebits_CurrentAccounts_CurrentAccountId",
                        column: x => x.CurrentAccountId,
                        principalTable: "CurrentAccounts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DirectDebits_GadgetInsurancePolicies_GadgetInsurancePolicyId",
                        column: x => x.GadgetInsurancePolicyId,
                        principalTable: "GadgetInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DirectDebits_GeneralItems_GeneralItemId",
                        column: x => x.GeneralItemId,
                        principalTable: "GeneralItems",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DirectDebits_LifeInsurancePolicies_LifeInsurancePolicyId",
                        column: x => x.LifeInsurancePolicyId,
                        principalTable: "LifeInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DirectDebits_Loans_LoanId",
                        column: x => x.LoanId,
                        principalTable: "Loans",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DirectDebits_Mortgages_MortgageId",
                        column: x => x.MortgageId,
                        principalTable: "Mortgages",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DirectDebits_Tenancies_TenancyId",
                        column: x => x.TenancyId,
                        principalTable: "Tenancies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DirectDebits_TravelInsurancePolicies_TravelInsurancePolicyId",
                        column: x => x.TravelInsurancePolicyId,
                        principalTable: "TravelInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DirectDebits_UtilityBills_UtilityBillId",
                        column: x => x.UtilityBillId,
                        principalTable: "UtilityBills",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DirectDebits_VehicleFinanceAgreements_VehicleFinanceAgreementId",
                        column: x => x.VehicleFinanceAgreementId,
                        principalTable: "VehicleFinanceAgreements",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DirectDebits_VehicleInsurancePolicies_VehicleInsurancePolicyId",
                        column: x => x.VehicleInsurancePolicyId,
                        principalTable: "VehicleInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DirectDebits_VehicleTaxes_VehicleTaxId",
                        column: x => x.VehicleTaxId,
                        principalTable: "VehicleTaxes",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "StandingOrders",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    CurrentAccountId = table.Column<int>(type: "int", nullable: false),
                    GeneralItemId = table.Column<int>(type: "int", nullable: true),
                    VehicleTaxId = table.Column<int>(type: "int", nullable: true),
                    VehicleFinanceAgreementId = table.Column<int>(type: "int", nullable: true),
                    LoanId = table.Column<int>(type: "int", nullable: true),
                    MortgageId = table.Column<int>(type: "int", nullable: true),
                    LifeInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    TravelInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    GadgetInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    VehicleInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    AddressInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    TenancyId = table.Column<int>(type: "int", nullable: true),
                    UtilityBillId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PaymentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentFrequency = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    PaymentDayOfMonth = table.Column<string>(type: "nvarchar(4)", maxLength: 4, nullable: true),
                    PaymentMonth = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StandingOrders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_StandingOrders_AddressInsurancePolicies_AddressInsurancePolicyId",
                        column: x => x.AddressInsurancePolicyId,
                        principalTable: "AddressInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StandingOrders_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StandingOrders_CurrentAccounts_CurrentAccountId",
                        column: x => x.CurrentAccountId,
                        principalTable: "CurrentAccounts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StandingOrders_GadgetInsurancePolicies_GadgetInsurancePolicyId",
                        column: x => x.GadgetInsurancePolicyId,
                        principalTable: "GadgetInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StandingOrders_GeneralItems_GeneralItemId",
                        column: x => x.GeneralItemId,
                        principalTable: "GeneralItems",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StandingOrders_LifeInsurancePolicies_LifeInsurancePolicyId",
                        column: x => x.LifeInsurancePolicyId,
                        principalTable: "LifeInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StandingOrders_Loans_LoanId",
                        column: x => x.LoanId,
                        principalTable: "Loans",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StandingOrders_Mortgages_MortgageId",
                        column: x => x.MortgageId,
                        principalTable: "Mortgages",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StandingOrders_Tenancies_TenancyId",
                        column: x => x.TenancyId,
                        principalTable: "Tenancies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StandingOrders_TravelInsurancePolicies_TravelInsurancePolicyId",
                        column: x => x.TravelInsurancePolicyId,
                        principalTable: "TravelInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StandingOrders_UtilityBills_UtilityBillId",
                        column: x => x.UtilityBillId,
                        principalTable: "UtilityBills",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StandingOrders_VehicleFinanceAgreements_VehicleFinanceAgreementId",
                        column: x => x.VehicleFinanceAgreementId,
                        principalTable: "VehicleFinanceAgreements",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StandingOrders_VehicleInsurancePolicies_VehicleInsurancePolicyId",
                        column: x => x.VehicleInsurancePolicyId,
                        principalTable: "VehicleInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StandingOrders_VehicleTaxes_VehicleTaxId",
                        column: x => x.VehicleTaxId,
                        principalTable: "VehicleTaxes",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Reminders",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<string>(type: "nvarchar(225)", maxLength: 225, nullable: false),
                    GeneralItemId = table.Column<int>(type: "int", nullable: true),
                    DrivingLicenceId = table.Column<int>(type: "int", nullable: true),
                    PassportId = table.Column<int>(type: "int", nullable: true),
                    VehicleTaxId = table.Column<int>(type: "int", nullable: true),
                    VehicleInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    VehicleMotId = table.Column<int>(type: "int", nullable: true),
                    VehicleId = table.Column<int>(type: "int", nullable: true),
                    VehicleBreakdownPolicyId = table.Column<int>(type: "int", nullable: true),
                    AddressInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    TenancyId = table.Column<int>(type: "int", nullable: true),
                    UtilityBillId = table.Column<int>(type: "int", nullable: true),
                    MortgageId = table.Column<int>(type: "int", nullable: true),
                    CreditCardId = table.Column<int>(type: "int", nullable: true),
                    LoanId = table.Column<int>(type: "int", nullable: true),
                    LifeInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    DebitCardId = table.Column<int>(type: "int", nullable: true),
                    RecurringPaymentId = table.Column<int>(type: "int", nullable: true),
                    TravelInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    GadgetInsurancePolicyId = table.Column<int>(type: "int", nullable: true),
                    GlobalHealthInsuranceCardId = table.Column<int>(type: "int", nullable: true),
                    VehicleWarrantyId = table.Column<int>(type: "int", nullable: true),
                    VehicleServicePlanId = table.Column<int>(type: "int", nullable: true),
                    VehicleFinanceAgreementId = table.Column<int>(type: "int", nullable: true),
                    EyeTestId = table.Column<int>(type: "int", nullable: true),
                    SavingsAccountId = table.Column<int>(type: "int", nullable: true),
                    SavingsAccountPotId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ReminderDate = table.Column<DateOnly>(type: "date", nullable: false),
                    Number = table.Column<int>(type: "int", nullable: false),
                    IsAutomatic = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Reminders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Reminders_AddressInsurancePolicies_AddressInsurancePolicyId",
                        column: x => x.AddressInsurancePolicyId,
                        principalTable: "AddressInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Reminders_CreditCards_CreditCardId",
                        column: x => x.CreditCardId,
                        principalTable: "CreditCards",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_DebitCards_DebitCardId",
                        column: x => x.DebitCardId,
                        principalTable: "DebitCards",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_DrivingLicences_DrivingLicenceId",
                        column: x => x.DrivingLicenceId,
                        principalTable: "DrivingLicences",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_EyeTests_EyeTestId",
                        column: x => x.EyeTestId,
                        principalTable: "EyeTests",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_GadgetInsurancePolicies_GadgetInsurancePolicyId",
                        column: x => x.GadgetInsurancePolicyId,
                        principalTable: "GadgetInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_GeneralItems_GeneralItemId",
                        column: x => x.GeneralItemId,
                        principalTable: "GeneralItems",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_GlobalHealthInsuranceCards_GlobalHealthInsuranceCardId",
                        column: x => x.GlobalHealthInsuranceCardId,
                        principalTable: "GlobalHealthInsuranceCards",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_LifeInsurancePolicies_LifeInsurancePolicyId",
                        column: x => x.LifeInsurancePolicyId,
                        principalTable: "LifeInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_Loans_LoanId",
                        column: x => x.LoanId,
                        principalTable: "Loans",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_Mortgages_MortgageId",
                        column: x => x.MortgageId,
                        principalTable: "Mortgages",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_Passports_PassportId",
                        column: x => x.PassportId,
                        principalTable: "Passports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_RecurringPayments_RecurringPaymentId",
                        column: x => x.RecurringPaymentId,
                        principalTable: "RecurringPayments",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_SavingsAccountPots_SavingsAccountPotId",
                        column: x => x.SavingsAccountPotId,
                        principalTable: "SavingsAccountPots",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_SavingsAccounts_SavingsAccountId",
                        column: x => x.SavingsAccountId,
                        principalTable: "SavingsAccounts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_Tenancies_TenancyId",
                        column: x => x.TenancyId,
                        principalTable: "Tenancies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_TravelInsurancePolicies_TravelInsurancePolicyId",
                        column: x => x.TravelInsurancePolicyId,
                        principalTable: "TravelInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_UtilityBills_UtilityBillId",
                        column: x => x.UtilityBillId,
                        principalTable: "UtilityBills",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_VehicleBreakdownPolicies_VehicleBreakdownPolicyId",
                        column: x => x.VehicleBreakdownPolicyId,
                        principalTable: "VehicleBreakdownPolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_VehicleFinanceAgreements_VehicleFinanceAgreementId",
                        column: x => x.VehicleFinanceAgreementId,
                        principalTable: "VehicleFinanceAgreements",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_VehicleInsurancePolicies_VehicleInsurancePolicyId",
                        column: x => x.VehicleInsurancePolicyId,
                        principalTable: "VehicleInsurancePolicies",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_VehicleMots_VehicleMotId",
                        column: x => x.VehicleMotId,
                        principalTable: "VehicleMots",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_VehicleServicePlans_VehicleServicePlanId",
                        column: x => x.VehicleServicePlanId,
                        principalTable: "VehicleServicePlans",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_VehicleTaxes_VehicleTaxId",
                        column: x => x.VehicleTaxId,
                        principalTable: "VehicleTaxes",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_VehicleWarranties_VehicleWarrantyId",
                        column: x => x.VehicleWarrantyId,
                        principalTable: "VehicleWarranties",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Reminders_Vehicles_VehicleId",
                        column: x => x.VehicleId,
                        principalTable: "Vehicles",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_Addresses_UserId",
                table: "Addresses",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AddressInsurancePolicies_AddressId",
                table: "AddressInsurancePolicies",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_AddressInsurancePolicies_UserId",
                table: "AddressInsurancePolicies",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CreditCardAprHistoryRecords_CreditCardId",
                table: "CreditCardAprHistoryRecords",
                column: "CreditCardId");

            migrationBuilder.CreateIndex(
                name: "IX_CreditCardAprHistoryRecords_UserId",
                table: "CreditCardAprHistoryRecords",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CreditCards_AddressId",
                table: "CreditCards",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_CreditCards_UserId",
                table: "CreditCards",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CurrentAccountAprHistoryRecords_CurrentAccountId",
                table: "CurrentAccountAprHistoryRecords",
                column: "CurrentAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_CurrentAccountAprHistoryRecords_UserId",
                table: "CurrentAccountAprHistoryRecords",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CurrentAccounts_AddressId",
                table: "CurrentAccounts",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_CurrentAccounts_UserId",
                table: "CurrentAccounts",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_DebitCards_CurrentAccountId",
                table: "DebitCards",
                column: "CurrentAccountId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DebitCards_UserId",
                table: "DebitCards",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Dentists_AddressId",
                table: "Dentists",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_Dentists_UserId",
                table: "Dentists",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_DirectDebits_AddressInsurancePolicyId",
                table: "DirectDebits",
                column: "AddressInsurancePolicyId",
                unique: true,
                filter: "[AddressInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DirectDebits_CurrentAccountId",
                table: "DirectDebits",
                column: "CurrentAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_DirectDebits_GadgetInsurancePolicyId",
                table: "DirectDebits",
                column: "GadgetInsurancePolicyId",
                unique: true,
                filter: "[GadgetInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DirectDebits_GeneralItemId",
                table: "DirectDebits",
                column: "GeneralItemId",
                unique: true,
                filter: "[GeneralItemId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DirectDebits_LifeInsurancePolicyId",
                table: "DirectDebits",
                column: "LifeInsurancePolicyId",
                unique: true,
                filter: "[LifeInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DirectDebits_LoanId",
                table: "DirectDebits",
                column: "LoanId",
                unique: true,
                filter: "[LoanId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DirectDebits_MortgageId",
                table: "DirectDebits",
                column: "MortgageId",
                unique: true,
                filter: "[MortgageId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DirectDebits_TenancyId",
                table: "DirectDebits",
                column: "TenancyId",
                unique: true,
                filter: "[TenancyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DirectDebits_TravelInsurancePolicyId",
                table: "DirectDebits",
                column: "TravelInsurancePolicyId",
                unique: true,
                filter: "[TravelInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DirectDebits_UserId",
                table: "DirectDebits",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_DirectDebits_UtilityBillId",
                table: "DirectDebits",
                column: "UtilityBillId",
                unique: true,
                filter: "[UtilityBillId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DirectDebits_VehicleFinanceAgreementId",
                table: "DirectDebits",
                column: "VehicleFinanceAgreementId",
                unique: true,
                filter: "[VehicleFinanceAgreementId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DirectDebits_VehicleInsurancePolicyId",
                table: "DirectDebits",
                column: "VehicleInsurancePolicyId",
                unique: true,
                filter: "[VehicleInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DirectDebits_VehicleTaxId",
                table: "DirectDebits",
                column: "VehicleTaxId",
                unique: true,
                filter: "[VehicleTaxId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DrivingLicences_AddressId",
                table: "DrivingLicences",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_DrivingLicences_UserId",
                table: "DrivingLicences",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_EyeTests_UserId",
                table: "EyeTests",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_GadgetInsurancePolicies_AddressId",
                table: "GadgetInsurancePolicies",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_GadgetInsurancePolicies_UserId",
                table: "GadgetInsurancePolicies",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_GeneralItems_AddressId",
                table: "GeneralItems",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_GeneralItems_UserId",
                table: "GeneralItems",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_GlobalHealthInsuranceCards_UserId",
                table: "GlobalHealthInsuranceCards",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_GpPractices_AddressId",
                table: "GpPractices",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_GpPractices_UserId",
                table: "GpPractices",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_LifeInsurancePolicies_AddressId",
                table: "LifeInsurancePolicies",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_LifeInsurancePolicies_UserId",
                table: "LifeInsurancePolicies",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Loans_AddressId",
                table: "Loans",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_Loans_UserId",
                table: "Loans",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Mortgages_AddressId",
                table: "Mortgages",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_Mortgages_UserId",
                table: "Mortgages",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Passports_UserId",
                table: "Passports",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_PensionBalanceHistoryRecords_PensionId",
                table: "PensionBalanceHistoryRecords",
                column: "PensionId");

            migrationBuilder.CreateIndex(
                name: "IX_PensionBalanceHistoryRecords_UserId",
                table: "PensionBalanceHistoryRecords",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Pensions_AddressId",
                table: "Pensions",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_Pensions_UserId",
                table: "Pensions",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_RecurringPayments_CreditCardId",
                table: "RecurringPayments",
                column: "CreditCardId");

            migrationBuilder.CreateIndex(
                name: "IX_RecurringPayments_DebitCardId",
                table: "RecurringPayments",
                column: "DebitCardId");

            migrationBuilder.CreateIndex(
                name: "IX_RecurringPayments_UserId",
                table: "RecurringPayments",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_AddressInsurancePolicyId",
                table: "Reminders",
                column: "AddressInsurancePolicyId",
                unique: true,
                filter: "[AddressInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_CreditCardId",
                table: "Reminders",
                column: "CreditCardId");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_DebitCardId",
                table: "Reminders",
                column: "DebitCardId",
                unique: true,
                filter: "[DebitCardId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_DrivingLicenceId",
                table: "Reminders",
                column: "DrivingLicenceId");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_EyeTestId",
                table: "Reminders",
                column: "EyeTestId",
                unique: true,
                filter: "[EyeTestId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_GadgetInsurancePolicyId",
                table: "Reminders",
                column: "GadgetInsurancePolicyId",
                unique: true,
                filter: "[GadgetInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_GeneralItemId",
                table: "Reminders",
                column: "GeneralItemId",
                unique: true,
                filter: "[GeneralItemId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_GlobalHealthInsuranceCardId",
                table: "Reminders",
                column: "GlobalHealthInsuranceCardId",
                unique: true,
                filter: "[GlobalHealthInsuranceCardId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_LifeInsurancePolicyId",
                table: "Reminders",
                column: "LifeInsurancePolicyId",
                unique: true,
                filter: "[LifeInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_LoanId",
                table: "Reminders",
                column: "LoanId",
                unique: true,
                filter: "[LoanId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_MortgageId",
                table: "Reminders",
                column: "MortgageId");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_PassportId",
                table: "Reminders",
                column: "PassportId",
                unique: true,
                filter: "[PassportId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_RecurringPaymentId",
                table: "Reminders",
                column: "RecurringPaymentId",
                unique: true,
                filter: "[RecurringPaymentId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_SavingsAccountId",
                table: "Reminders",
                column: "SavingsAccountId",
                unique: true,
                filter: "[SavingsAccountId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_SavingsAccountPotId",
                table: "Reminders",
                column: "SavingsAccountPotId",
                unique: true,
                filter: "[SavingsAccountPotId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_TenancyId",
                table: "Reminders",
                column: "TenancyId",
                unique: true,
                filter: "[TenancyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_TravelInsurancePolicyId",
                table: "Reminders",
                column: "TravelInsurancePolicyId",
                unique: true,
                filter: "[TravelInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_UserId",
                table: "Reminders",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_UtilityBillId",
                table: "Reminders",
                column: "UtilityBillId");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_VehicleBreakdownPolicyId",
                table: "Reminders",
                column: "VehicleBreakdownPolicyId",
                unique: true,
                filter: "[VehicleBreakdownPolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_VehicleFinanceAgreementId",
                table: "Reminders",
                column: "VehicleFinanceAgreementId",
                unique: true,
                filter: "[VehicleFinanceAgreementId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_VehicleId",
                table: "Reminders",
                column: "VehicleId",
                unique: true,
                filter: "[VehicleId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_VehicleInsurancePolicyId",
                table: "Reminders",
                column: "VehicleInsurancePolicyId",
                unique: true,
                filter: "[VehicleInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_VehicleMotId",
                table: "Reminders",
                column: "VehicleMotId",
                unique: true,
                filter: "[VehicleMotId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_VehicleServicePlanId",
                table: "Reminders",
                column: "VehicleServicePlanId",
                unique: true,
                filter: "[VehicleServicePlanId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_VehicleTaxId",
                table: "Reminders",
                column: "VehicleTaxId",
                unique: true,
                filter: "[VehicleTaxId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Reminders_VehicleWarrantyId",
                table: "Reminders",
                column: "VehicleWarrantyId",
                unique: true,
                filter: "[VehicleWarrantyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_SavingsAccountAprHistoryRecords_SavingsAccountId",
                table: "SavingsAccountAprHistoryRecords",
                column: "SavingsAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_SavingsAccountAprHistoryRecords_UserId",
                table: "SavingsAccountAprHistoryRecords",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_SavingsAccountBalanceHistoryRecords_SavingsAccountId",
                table: "SavingsAccountBalanceHistoryRecords",
                column: "SavingsAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_SavingsAccountBalanceHistoryRecords_UserId",
                table: "SavingsAccountBalanceHistoryRecords",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_SavingsAccountPotAprHistoryRecords_SavingsAccountPotId",
                table: "SavingsAccountPotAprHistoryRecords",
                column: "SavingsAccountPotId");

            migrationBuilder.CreateIndex(
                name: "IX_SavingsAccountPotAprHistoryRecords_UserId",
                table: "SavingsAccountPotAprHistoryRecords",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_SavingsAccountPotBalanceHistoryRecords_SavingsAccountPotId",
                table: "SavingsAccountPotBalanceHistoryRecords",
                column: "SavingsAccountPotId");

            migrationBuilder.CreateIndex(
                name: "IX_SavingsAccountPotBalanceHistoryRecords_UserId",
                table: "SavingsAccountPotBalanceHistoryRecords",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_SavingsAccountPots_SavingsAccountId",
                table: "SavingsAccountPots",
                column: "SavingsAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_SavingsAccountPots_UserId",
                table: "SavingsAccountPots",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_SavingsAccounts_AddressId",
                table: "SavingsAccounts",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_SavingsAccounts_UserId",
                table: "SavingsAccounts",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_StandingOrders_AddressInsurancePolicyId",
                table: "StandingOrders",
                column: "AddressInsurancePolicyId",
                unique: true,
                filter: "[AddressInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_StandingOrders_CurrentAccountId",
                table: "StandingOrders",
                column: "CurrentAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_StandingOrders_GadgetInsurancePolicyId",
                table: "StandingOrders",
                column: "GadgetInsurancePolicyId",
                unique: true,
                filter: "[GadgetInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_StandingOrders_GeneralItemId",
                table: "StandingOrders",
                column: "GeneralItemId",
                unique: true,
                filter: "[GeneralItemId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_StandingOrders_LifeInsurancePolicyId",
                table: "StandingOrders",
                column: "LifeInsurancePolicyId",
                unique: true,
                filter: "[LifeInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_StandingOrders_LoanId",
                table: "StandingOrders",
                column: "LoanId",
                unique: true,
                filter: "[LoanId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_StandingOrders_MortgageId",
                table: "StandingOrders",
                column: "MortgageId",
                unique: true,
                filter: "[MortgageId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_StandingOrders_TenancyId",
                table: "StandingOrders",
                column: "TenancyId",
                unique: true,
                filter: "[TenancyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_StandingOrders_TravelInsurancePolicyId",
                table: "StandingOrders",
                column: "TravelInsurancePolicyId",
                unique: true,
                filter: "[TravelInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_StandingOrders_UserId",
                table: "StandingOrders",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_StandingOrders_UtilityBillId",
                table: "StandingOrders",
                column: "UtilityBillId",
                unique: true,
                filter: "[UtilityBillId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_StandingOrders_VehicleFinanceAgreementId",
                table: "StandingOrders",
                column: "VehicleFinanceAgreementId",
                unique: true,
                filter: "[VehicleFinanceAgreementId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_StandingOrders_VehicleInsurancePolicyId",
                table: "StandingOrders",
                column: "VehicleInsurancePolicyId",
                unique: true,
                filter: "[VehicleInsurancePolicyId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_StandingOrders_VehicleTaxId",
                table: "StandingOrders",
                column: "VehicleTaxId",
                unique: true,
                filter: "[VehicleTaxId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_TaxableInterestPayments_CurrentAccountId",
                table: "TaxableInterestPayments",
                column: "CurrentAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_TaxableInterestPayments_SavingsAccountId",
                table: "TaxableInterestPayments",
                column: "SavingsAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_TaxableInterestPayments_SavingsAccountPotId",
                table: "TaxableInterestPayments",
                column: "SavingsAccountPotId");

            migrationBuilder.CreateIndex(
                name: "IX_TaxableInterestPayments_UserId",
                table: "TaxableInterestPayments",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Tenancies_AddressId",
                table: "Tenancies",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_Tenancies_UserId",
                table: "Tenancies",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_TravelInsurancePolicies_AddressId",
                table: "TravelInsurancePolicies",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_TravelInsurancePolicies_UserId",
                table: "TravelInsurancePolicies",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UtilityBills_AddressId",
                table: "UtilityBills",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_UtilityBills_UserId",
                table: "UtilityBills",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleBreakdownPolicies_AddressId",
                table: "VehicleBreakdownPolicies",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleBreakdownPolicies_UserId",
                table: "VehicleBreakdownPolicies",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleBreakdownPolicies_VehicleId",
                table: "VehicleBreakdownPolicies",
                column: "VehicleId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleFinanceAgreements_AddressId",
                table: "VehicleFinanceAgreements",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleFinanceAgreements_UserId",
                table: "VehicleFinanceAgreements",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleFinanceAgreements_VehicleId",
                table: "VehicleFinanceAgreements",
                column: "VehicleId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleInsuranceClaims_UserId",
                table: "VehicleInsuranceClaims",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleInsuranceClaims_VehicleInsurancePolicyId",
                table: "VehicleInsuranceClaims",
                column: "VehicleInsurancePolicyId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleInsurancePolicies_AddressId",
                table: "VehicleInsurancePolicies",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleInsurancePolicies_UserId",
                table: "VehicleInsurancePolicies",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleInsurancePolicies_VehicleId",
                table: "VehicleInsurancePolicies",
                column: "VehicleId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleMots_UserId",
                table: "VehicleMots",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleMots_VehicleId",
                table: "VehicleMots",
                column: "VehicleId");

            migrationBuilder.CreateIndex(
                name: "IX_Vehicles_AddressId",
                table: "Vehicles",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_Vehicles_UserId",
                table: "Vehicles",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleServicePlans_UserId",
                table: "VehicleServicePlans",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleServicePlans_VehicleId",
                table: "VehicleServicePlans",
                column: "VehicleId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleServices_UserId",
                table: "VehicleServices",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleServices_VehicleId",
                table: "VehicleServices",
                column: "VehicleId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleTaxes_UserId",
                table: "VehicleTaxes",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleTaxes_VehicleId",
                table: "VehicleTaxes",
                column: "VehicleId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleWarranties_UserId",
                table: "VehicleWarranties",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleWarranties_VehicleId",
                table: "VehicleWarranties",
                column: "VehicleId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CreditCardAprHistoryRecords");

            migrationBuilder.DropTable(
                name: "CurrentAccountAprHistoryRecords");

            migrationBuilder.DropTable(
                name: "Dentists");

            migrationBuilder.DropTable(
                name: "DirectDebits");

            migrationBuilder.DropTable(
                name: "GpPractices");

            migrationBuilder.DropTable(
                name: "PensionBalanceHistoryRecords");

            migrationBuilder.DropTable(
                name: "Reminders");

            migrationBuilder.DropTable(
                name: "SavingsAccountAprHistoryRecords");

            migrationBuilder.DropTable(
                name: "SavingsAccountBalanceHistoryRecords");

            migrationBuilder.DropTable(
                name: "SavingsAccountPotAprHistoryRecords");

            migrationBuilder.DropTable(
                name: "SavingsAccountPotBalanceHistoryRecords");

            migrationBuilder.DropTable(
                name: "StandingOrders");

            migrationBuilder.DropTable(
                name: "TaxableInterestPayments");

            migrationBuilder.DropTable(
                name: "VehicleInsuranceClaims");

            migrationBuilder.DropTable(
                name: "VehicleServices");

            migrationBuilder.DropTable(
                name: "Pensions");

            migrationBuilder.DropTable(
                name: "DrivingLicences");

            migrationBuilder.DropTable(
                name: "EyeTests");

            migrationBuilder.DropTable(
                name: "GlobalHealthInsuranceCards");

            migrationBuilder.DropTable(
                name: "Passports");

            migrationBuilder.DropTable(
                name: "RecurringPayments");

            migrationBuilder.DropTable(
                name: "VehicleBreakdownPolicies");

            migrationBuilder.DropTable(
                name: "VehicleMots");

            migrationBuilder.DropTable(
                name: "VehicleServicePlans");

            migrationBuilder.DropTable(
                name: "VehicleWarranties");

            migrationBuilder.DropTable(
                name: "AddressInsurancePolicies");

            migrationBuilder.DropTable(
                name: "GadgetInsurancePolicies");

            migrationBuilder.DropTable(
                name: "GeneralItems");

            migrationBuilder.DropTable(
                name: "LifeInsurancePolicies");

            migrationBuilder.DropTable(
                name: "Loans");

            migrationBuilder.DropTable(
                name: "Mortgages");

            migrationBuilder.DropTable(
                name: "Tenancies");

            migrationBuilder.DropTable(
                name: "TravelInsurancePolicies");

            migrationBuilder.DropTable(
                name: "UtilityBills");

            migrationBuilder.DropTable(
                name: "VehicleFinanceAgreements");

            migrationBuilder.DropTable(
                name: "VehicleTaxes");

            migrationBuilder.DropTable(
                name: "SavingsAccountPots");

            migrationBuilder.DropTable(
                name: "VehicleInsurancePolicies");

            migrationBuilder.DropTable(
                name: "CreditCards");

            migrationBuilder.DropTable(
                name: "DebitCards");

            migrationBuilder.DropTable(
                name: "SavingsAccounts");

            migrationBuilder.DropTable(
                name: "Vehicles");

            migrationBuilder.DropTable(
                name: "CurrentAccounts");

            migrationBuilder.DropTable(
                name: "Addresses");
        }
    }
}
