@page
@model Life.Pages.Reminders.CreateModel
@{
    ViewData["Title"] = "Create Reminder";
}


<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Create Reminder</h2>

                </div>
                <div class="card-body">
                    <form method="post" class="row g-3 mt-1" autocomplete="off">

                        <div class="row my-3">
                            <small class="text-muted">Required fields are marked in <strong>bold</strong>.</small>
                        </div>

                        <div class="row my-3">
                            <label asp-for="Name" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="Name" class="form-control" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="ReminderDate" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="ReminderDate" class="form-control" type="date" />
                                <span asp-validation-for="ReminderDate" class="text-danger"></span>
                            </div>
                        </div>

                        

                        <div class="row my-3">
                            <div class="col-sm-10 offset-sm-2">
                                <button type="submit" class="btn btn-sm btn-primary">Create</button>
                                <a asp-page="./Index" class="btn btn-sm btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>


@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
