using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class UtilityBill
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        public int AddressId { get; set; }
        
        [ForeignKey("AddressId")]
        public Address Address { get; set; } = null!;
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string Provider { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? AccountNumber { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? PaymentAmount { get; set; }

        [StringLength(20)]
        public string? PaymentFrequency { get; set; }

        [StringLength(4)]
        public string? PaymentDayOfMonth { get; set; }

        [StringLength(20)]
        public string? PaymentMonth { get; set; }

        [StringLength(50)]
        public string? PaymentMethod { get; set; }

        public DateOnly? ContractStartDate { get; set; }

        public DateOnly? ContractEndDate { get; set; }

        public DateOnly? TariffStartDate { get; set; }

        public DateOnly? TariffEndDate { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? GasPencePerDay { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? GasPencePerKWh { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? ElectricityPencePerDay { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? ElectricityPencePerKWh { get; set; }

        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public DirectDebit? DirectDebit { get; set; }
        public StandingOrder? StandingOrder { get; set; }
        public ICollection<Reminder> Reminders { get; set; } = new List<Reminder>();
    }
}
