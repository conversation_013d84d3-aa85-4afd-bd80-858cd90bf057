using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class Reminder
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        // Polymorphic Foreign Keys (All NULLABLE) - exactly as defined in Entities.md
        public int? GeneralItemId { get; set; }
        [ForeignKey("GeneralItemId")]
        public GeneralItem? GeneralItem { get; set; }

        public int? DrivingLicenceId { get; set; }
        [ForeignKey("DrivingLicenceId")]
        public DrivingLicence? DrivingLicence { get; set; }

        public int? PassportId { get; set; }
        [ForeignKey("PassportId")]
        public Passport? Passport { get; set; }

        public int? VehicleTaxId { get; set; }
        [ForeignKey("VehicleTaxId")]
        public VehicleTax? VehicleTax { get; set; }

        public int? VehicleInsurancePolicyId { get; set; }
        [ForeignKey("VehicleInsurancePolicyId")]
        public VehicleInsurancePolicy? VehicleInsurancePolicy { get; set; }

        public int? VehicleMotId { get; set; }
        [ForeignKey("VehicleMotId")]
        public VehicleMot? VehicleMot { get; set; }

        public int? VehicleId { get; set; }
        [ForeignKey("VehicleId")]
        public Vehicle? Vehicle { get; set; }

        public int? VehicleBreakdownPolicyId { get; set; }
        [ForeignKey("VehicleBreakdownPolicyId")]
        public VehicleBreakdownPolicy? VehicleBreakdownPolicy { get; set; }

        public int? AddressInsurancePolicyId { get; set; }
        [ForeignKey("AddressInsurancePolicyId")]
        public AddressInsurancePolicy? AddressInsurancePolicy { get; set; }

        public int? TenancyId { get; set; }
        [ForeignKey("TenancyId")]
        public Tenancy? Tenancy { get; set; }

        public int? UtilityBillId { get; set; }
        [ForeignKey("UtilityBillId")]
        public UtilityBill? UtilityBill { get; set; }
        
        public int? MortgageId { get; set; }
        [ForeignKey("MortgageId")]
        public Mortgage? Mortgage { get; set; }

        public int? CreditCardId { get; set; }
        [ForeignKey("CreditCardId")]
        public CreditCard? CreditCard { get; set; }

        public int? LoanId { get; set; }
        [ForeignKey("LoanId")]
        public Loan? Loan { get; set; }

        public int? LifeInsurancePolicyId { get; set; }
        [ForeignKey("LifeInsurancePolicyId")]
        public LifeInsurancePolicy? LifeInsurancePolicy { get; set; }

        public int? DebitCardId { get; set; }
        [ForeignKey("DebitCardId")]
        public DebitCard? DebitCard { get; set; }

        public int? RecurringPaymentId { get; set; }
        [ForeignKey("RecurringPaymentId")]
        public RecurringPayment? RecurringPayment { get; set; }

        public int? TravelInsurancePolicyId { get; set; }
        [ForeignKey("TravelInsurancePolicyId")]
        public TravelInsurancePolicy? TravelInsurancePolicy { get; set; }

        public int? GadgetInsurancePolicyId { get; set; }
        [ForeignKey("GadgetInsurancePolicyId")]
        public GadgetInsurancePolicy? GadgetInsurancePolicy { get; set; }

        public int? GlobalHealthInsuranceCardId { get; set; }
        [ForeignKey("GlobalHealthInsuranceCardId")]
        public GlobalHealthInsuranceCard? GlobalHealthInsuranceCard { get; set; }

        public int? VehicleWarrantyId { get; set; }
        [ForeignKey("VehicleWarrantyId")]
        public VehicleWarranty? VehicleWarranty { get; set; }

        public int? VehicleServicePlanId { get; set; }
        [ForeignKey("VehicleServicePlanId")]
        public VehicleServicePlan? VehicleServicePlan { get; set; }

        public int? VehicleFinanceAgreementId { get; set; }
        [ForeignKey("VehicleFinanceAgreementId")]
        public VehicleFinanceAgreement? VehicleFinanceAgreement { get; set; }

        public int? EyeTestId { get; set; }
        [ForeignKey("EyeTestId")]
        public EyeTest? EyeTest { get; set; }

        public int? SavingsAccountId { get; set; }
        [ForeignKey("SavingsAccountId")]
        public SavingsAccount? SavingsAccount { get; set; }

        public int? SavingsAccountPotId { get; set; }
        [ForeignKey("SavingsAccountPotId")]
        public SavingsAccountPot? SavingsAccountPot { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        public DateOnly ReminderDate { get; set; }

        [Required]
        public int Number { get; set; }

        [Required]
        public bool IsAutomatic { get; set; }
       
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
