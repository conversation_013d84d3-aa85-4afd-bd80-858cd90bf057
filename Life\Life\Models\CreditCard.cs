using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class CreditCard
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        public int? AddressId { get; set; }
        
        [ForeignKey("AddressId")]
        public Address? Address { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string Provider { get; set; } = string.Empty;
        
        [Required]
        public DateOnly ExpiryDate { get; set; }

        public DateOnly? StartDate { get; set; }

        public DateOnly? PromotionalPeriodEndDate { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Apr { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? CreditLimit { get; set; }
        
        [StringLength(4)]
        public string? PaymentDayOfMonth { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public ICollection<CreditCardAprHistoryRecord> CreditCardAprHistoryRecords { get; set; } = new List<CreditCardAprHistoryRecord>();
        public ICollection<RecurringPayment> RecurringPayments { get; set; } = new List<RecurringPayment>();
        public ICollection<Reminder> Reminders { get; set; } = new List<Reminder>();
    }
}
