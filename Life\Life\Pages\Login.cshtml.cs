using Life.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages
{
    [AllowAnonymous]
    public class LoginModel : PageModel
    {
        private readonly SignInManager<User> _signInManager;

        public LoginModel(SignInManager<User> signInManager)
        {
            _signInManager = signInManager;
        }

        [BindProperty]
        [Required(ErrorMessage = "Please enter your username.")]
        public string UserName { get; set; } = "";

        [BindProperty]
        [Required(ErrorMessage = "Please enter your password.")]
        [DataType(DataType.Password)]
        public string Password { get; set; } = "";

        [BindProperty]
        public bool RememberMe { get; set; }

        public string? ReturnUrl { get; set; }
        
        public IActionResult OnGet()
        {
            if (_signInManager.IsSignedIn(User))
            {
                return RedirectToPage("/Dashboard");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(string? returnurl)
        {
            returnurl ??= "/dashboard";

            if (ModelState.IsValid)
            {
                UserName = UserName.ToLowerInvariant();
                var result = await _signInManager.PasswordSignInAsync(UserName, Password, RememberMe, false);
                if (result.Succeeded)
                {
                    return LocalRedirect(returnurl);
                }
                else
                {
                    ModelState.AddModelError(string.Empty, "Login attempt failed.");
                }
            }

            return Page();

        }
    }
}
