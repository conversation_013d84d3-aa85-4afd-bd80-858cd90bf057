using Life.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages
{
    [AllowAnonymous]
    public class RegisterModel : PageModel
    {
        private readonly UserManager<User> _userManager;
        private readonly SignInManager<User> _signInManager;

        public RegisterModel(UserManager<User> userManager, SignInManager<User> signInManager)
        {
            _userManager = userManager;
            _signInManager = signInManager;
        }

        [BindProperty]
        [Required(ErrorMessage = "Please set a username for your account login.")]
        [MinLength(2, ErrorMessage = "Your username must be at least {1} characters long.")]
        [MaxLength(256, ErrorMessage = "Your username must be at most {1} characters long.")]
        public string UserName { get; set; } = "";

        [BindProperty]
        [Required(ErrorMessage = "Please set a password for your account login.")]
        [MinLength(6, ErrorMessage = "Your password must be at least {1} characters long.")]
        [DataType(DataType.Password)]
        public string Password { get; set; } = "";

        [BindProperty]
        [Required(ErrorMessage = "Please re-enter your password to confirm.")]
        [DataType(DataType.Password)]
        [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
        public string ConfirmPassword { get; set; } = "";

        public IActionResult OnGet()
        {
            if (_signInManager.IsSignedIn(User))
            {
                return RedirectToPage("/Dashboard");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (ModelState.IsValid)
            {
                UserName = UserName.ToLowerInvariant();
                var user = new User 
                { 
                    UserName = UserName, 
                    DateCreated = DateTime.Now 
                };

                var result = await _userManager.CreateAsync(user, Password);

                if (result.Succeeded)
                {
                    await _signInManager.SignInAsync(user, isPersistent: false);
                    return RedirectToPage("/Dashboard");
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
            }

            return Page();
        }
    }
}

