using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages.Passports
{
    public class CreateModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public CreateModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        [Required(ErrorMessage = "Name is required")]
        [StringLength(50, ErrorMessage = "Name cannot exceed 50 characters")]
        [DisplayName("Passport Name")]
        public string Name { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Passport Number is required")]
        [StringLength(10, ErrorMessage = "Passport Number cannot exceed 10 characters")]
        [DisplayName("Passport Number")]
        public string PassportNumber { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Start Date is required")]
        [DisplayName("Start Date")]
        public DateOnly StartDate { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Expiry Date is required")]
        [DisplayName("Expiry Date")]
        public DateOnly ExpiryDate { get; set; }

        [BindProperty]
        [Range(0, 9999999999999999.99, ErrorMessage = "Cost must be a valid amount")]
        [DisplayName("Cost")]
        public decimal? Cost { get; set; }

        [BindProperty]
        [StringLength(20, ErrorMessage = "Size cannot exceed 20 characters")]
        public string? Size { get; set; }

        public IActionResult OnGet()
        {
            // Set default dates
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow);
            ExpiryDate = DateOnly.FromDateTime(DateTime.UtcNow.AddYears(10));

            return Page();
        }   

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            // Validate that expiry date is after start date
            if (ExpiryDate <= StartDate)
            {
                ModelState.AddModelError(nameof(ExpiryDate), "Expiry date must be after start date");
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Create the passport
            var passport = new Passport
            {
                Name = Name,
                PassportNumber = PassportNumber,
                StartDate = StartDate,
                ExpiryDate = ExpiryDate,
                Cost = Cost,
                Size = Size,
                User = currentUser,
                UserId = currentUser.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Passports.Add(passport);
            await _context.SaveChangesAsync();

            // Create automatic reminder for passport expiry
            var reminder = new Reminder
            {
                Name = $"{Name} - Passport Expiry Date",
                ReminderDate = ExpiryDate,
                User = currentUser,
                UserId = currentUser.Id,
                PassportId = passport.Id,
                IsAutomatic = true,
                Number = 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Reminders.Add(reminder);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Passport created successfully";

            return RedirectToPage("./Index");
        }
    }
}
