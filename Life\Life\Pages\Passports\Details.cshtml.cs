using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.Passports
{
    public class DetailsModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DetailsModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public int PassportId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string PassportNumber { get; set; } = string.Empty;
        public DateOnly StartDate { get; set; }
        public DateOnly ExpiryDate { get; set; }
        public decimal? Cost { get; set; }
        public string? Size { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public bool HasLinkedReminder { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var passport = await _context.Passports
                .Where(p => p.Id == id && p.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (passport == null)
            {
                return NotFound();
            }

            PassportId = passport.Id;
            Name = passport.Name;
            PassportNumber = passport.PassportNumber;
            StartDate = passport.StartDate;
            ExpiryDate = passport.ExpiryDate;
            Cost = passport.Cost;
            Size = passport.Size;
            CreatedAt = passport.CreatedAt;
            UpdatedAt = passport.UpdatedAt;

            // Check if there's a linked automatic reminder
            HasLinkedReminder = await _context.Reminders
                .AnyAsync(r => r.PassportId == passport.Id && r.IsAutomatic == true);

            return Page();
        }
    }
}
