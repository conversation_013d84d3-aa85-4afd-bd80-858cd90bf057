using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class SavingsAccountPot
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        public int SavingsAccountId { get; set; }
        
        [ForeignKey("SavingsAccountId")]
        public SavingsAccount SavingsAccount { get; set; } = null!;
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? PotType { get; set; }
        
        public int? FixedTermMonths { get; set; }

        public DateOnly? FixedTermEndDate { get; set; }
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PotBalance { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Apr { get; set; }
        
        [StringLength(20)]
        public string? InterestFrequency { get; set; }
        
        public DateOnly? DateOpened { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public ICollection<SavingsAccountPotAprHistoryRecord> SavingsAccountPotAprHistoryRecords { get; set; } = new List<SavingsAccountPotAprHistoryRecord>();
        public ICollection<SavingsAccountPotBalanceHistoryRecord> SavingsAccountPotBalanceHistoryRecords { get; set; } = new List<SavingsAccountPotBalanceHistoryRecord>();
        public ICollection<TaxableInterestPayment> TaxableInterestPayments { get; set; } = new List<TaxableInterestPayment>();
        public Reminder? Reminder { get; set; }
    }
}
