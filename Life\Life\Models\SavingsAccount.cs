using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class SavingsAccount
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        public int? AddressId { get; set; }
        
        [ForeignKey("AddressId")]
        public Address? Address { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string Provider { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? AccountType { get; set; }
        
        public int? FixedTermMonths { get; set; }

        public DateOnly? FixedTermEndDate { get; set; }
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal AccountBalance { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Apr { get; set; }
        
        [StringLength(20)]
        public string? InterestFrequency { get; set; }
        
        public DateOnly? DateOpened { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public ICollection<SavingsAccountAprHistoryRecord> SavingsAccountAprHistoryRecords { get; set; } = new List<SavingsAccountAprHistoryRecord>();
        public ICollection<SavingsAccountBalanceHistoryRecord> SavingsAccountBalanceHistoryRecords { get; set; } = new List<SavingsAccountBalanceHistoryRecord>();
        public ICollection<SavingsAccountPot> SavingsAccountPots { get; set; } = new List<SavingsAccountPot>();
        public ICollection<TaxableInterestPayment> TaxableInterestPayments { get; set; } = new List<TaxableInterestPayment>();
        public Reminder? Reminder { get; set; }
    }
}
