﻿@page
@model Life.Pages.LogoutModel
@{
}


<div class="container">
    <section class="section min-vh-100 d-flex flex-column align-items-center justify-content-center py-4">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-4 col-md-6 d-flex flex-column align-items-center justify-content-center">
                    <div class="card mb-3">
                        <div class="card-body">

                            <div class="pb-2">
                                <h5 class="card-title text-center fs-4">Are you sure you want to logout?</h5>
                            </div>

                            <form method="post">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <button type="submit" class="btn btn-primary">Logout</button>
                                    <a asp-page="/Dashboard" class="btn btn-secondary">Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
