﻿@page
@model Life.Pages.RegisterModel
@{
    ViewData["Title"] = "Register";
}

<div class="container">
    <section class="section min-vh-100 d-flex flex-column align-items-center justify-content-center py-4">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-4 col-md-6 d-flex flex-column align-items-center justify-content-center">
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="d-flex justify-content-center pt-4">
                                <a href="~/" class="logo d-flex align-items-center w-auto">
                                    <img src="~/logo.png" alt="">
                                    <span>Life</span>
                                </a>
                            </div>

                            <div class="pb-2">
                                <h5 class="card-title text-center fs-4">Create an account</h5>     
                            </div>

                            <form method="post" class="row g-3">

                                <div asp-validation-summary="All" class="text-danger"></div>

                                <div class="col-12">
                                    <label asp-for="UserName" class="form-label">UserName</label>
                                    <div class="input-group has-validation">
                                        <input asp-for="UserName" class="form-control" autocomplete="username" required />
                                        <span asp-validation-for="UserName" class="invalid-feedback"></span>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <label asp-for="Password" class="form-label">Password</label>
                                    <input asp-for="Password" class="form-control" autocomplete="new-password" required />
                                    <span asp-validation-for="Password" class="invalid-feedback"></span>
                                </div>

                                <div class="col-12">
                                    <label asp-for="ConfirmPassword" class="form-label">Confirm Password</label>
                                    <input asp-for="ConfirmPassword" class="form-control" autocomplete="new-password" required />
                                    <span asp-validation-for="ConfirmPassword" class="invalid-feedback"></span>
                                </div>

                                <div class="col-12">
                                    <button class="btn btn-primary w-100" type="submit">Create Account</button>
                                </div>

                                <div class="col-12">
                                    <p class="small mb-0">Already have an account? <a asp-page="/Login">Login here</a></p>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}