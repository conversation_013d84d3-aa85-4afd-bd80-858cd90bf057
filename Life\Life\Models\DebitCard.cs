using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class DebitCard
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        public int CurrentAccountId { get; set; }
        
        [ForeignKey("CurrentAccountId")]
        public CurrentAccount CurrentAccount { get; set; } = null!;
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        public DateOnly ExpiryDate { get; set; }

        public DateOnly? StartDate { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public ICollection<RecurringPayment> RecurringPayments { get; set; } = new List<RecurringPayment>();
        public Reminder? Reminder { get; set; }
    }
}
