using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class Pension
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        public int? AddressId { get; set; }
        
        [ForeignKey("AddressId")]
        public Address? Address { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string Provider { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? Reference { get; set; }
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Balance { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MonthlyContributionAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MonthlyEmployerContributionAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? ContributionFeePercentage { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? AnnualChargePercentage { get; set; }
        
        public DateOnly? DateOpened { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public ICollection<PensionBalanceHistoryRecord> PensionBalanceHistoryRecords { get; set; } = new List<PensionBalanceHistoryRecord>();
    }
}
