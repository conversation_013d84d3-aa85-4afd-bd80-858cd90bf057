@page
@model Life.Pages.Passports.IndexModel
@{
    ViewData["Title"] = "Passports";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Passports</h2>
                    <a asp-page="./Create" class="btn btn-sm btn-outline-success add-button">
                        <i class="bi bi-plus-square"></i>
                    </a>
                </div>
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="col-12 d-flex flex-column align-items-center justify-content-center">
                        <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
					</div>
                }

                @if (Model.Passports.Any())
                {
                    <div class="card-body">
                        <h5 class="card-title">Current Passports</h5>

                        <div class="list-group">
                            @foreach (var passport in Model.Passports)
                            {
                                <div class="list-group-item py-3">
                                    <div class="d-flex justify-content-between my-3">
                                        <div>
                                            <h5 class="mb-2 text-break">@passport.Name</h5>
                                            <h6>Expires: @passport.ExpiryDate.ToString("dd/MM/yyyy")</h6>
                                            <h6>Passport Number: @passport.PassportNumber</h6>
                                            <div class="mt-2">
                                                <a asp-page="./Details" asp-route-id="@passport.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i> Details
                                                </a>
                                                <a asp-page="./Edit" asp-route-id="@passport.Id" class="btn btn-sm btn-outline-secondary">
                                                    <i class="bi bi-pencil"></i> Edit
                                                </a>
                                            </div>
                                        </div>
                                        <a asp-page="./Delete" asp-route-id="@passport.Id" class="btn btn-sm btn-outline-danger">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                } 
                else
                {
                    <div class="card-body">
                        <h5 class="card-title">Current Passports</h5>
                        <p>There are no current passports</p>
                    </div>
                }

                @if (Model.ExpiredPassports.Any())
                {
                    <hr class="mt-5" />
                    <div class="card-body expired-items">
                        <h5 class="card-title">Expired Passports</h5>

                        <div class="list-group">
                            @foreach (var passport in Model.ExpiredPassports)
                            {
                                <div class="list-group-item py-3">
                                    <div class="d-flex justify-content-between my-3">
                                        <div>
                                            <h5 class="mb-2 text-break">@passport.Name</h5>
                                            <h6 class="text-muted">Expired: @passport.ExpiryDate.ToString("dd/MM/yyyy")</h6>
                                            <small class="text-muted">Passport Number: @passport.PassportNumber</small>
                                            <div class="mt-2">
                                                <a asp-page="./Details" asp-route-id="@passport.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i> Details
                                                </a>
                                                <a asp-page="./Edit" asp-route-id="@passport.Id" class="btn btn-sm btn-outline-secondary">
                                                    <i class="bi bi-pencil"></i> Edit
                                                </a>
                                            </div>
                                        </div>
                                        <a asp-page="./Delete" asp-route-id="@passport.Id" class="btn btn-sm btn-outline-danger">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
