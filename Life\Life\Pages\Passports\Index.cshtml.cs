using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.Passports
{
    public class IndexModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public IndexModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public IList<Passport> Passports { get; set; } = default!;
        public IList<Passport> ExpiredPassports { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();
            
            // Get current (non-expired) passports
            Passports = await _context.Passports
                .AsNoTracking()
                .Where(p => p.UserId == currentUser.Id)
                .Where(p => p.ExpiryDate >= DateOnly.FromDateTime(DateTime.UtcNow))
                .OrderBy(p => p.ExpiryDate)
                .ToListAsync();

            // Get expired passports
            ExpiredPassports = await _context.Passports
                .AsNoTracking()
                .Where(p => p.UserId == currentUser.Id)
                .Where(p => p.ExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow))
                .OrderBy(p => p.ExpiryDate)
                .ToListAsync();

            return Page();
        }
    }
}
