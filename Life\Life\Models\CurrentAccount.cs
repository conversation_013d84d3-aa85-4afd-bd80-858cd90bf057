using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class CurrentAccount
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        public int? AddressId { get; set; }
        
        [ForeignKey("AddressId")]
        public Address? Address { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string Provider { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MonthlyFee { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MinimumMonthlyFunding { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? OverdraftLimit { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? OverdraftApr { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? InterestApr { get; set; }
        
        public DateOnly? DateOpened { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public ICollection<CurrentAccountAprHistoryRecord> CurrentAccountAprHistoryRecords { get; set; } = new List<CurrentAccountAprHistoryRecord>();
        public DebitCard? DebitCard { get; set; }
        public ICollection<DirectDebit> DirectDebits { get; set; } = new List<DirectDebit>();
        public ICollection<StandingOrder> StandingOrders { get; set; } = new List<StandingOrder>();
        public ICollection<TaxableInterestPayment> TaxableInterestPayments { get; set; } = new List<TaxableInterestPayment>();
    }
}
