using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages.Reminders
{
    public class EditModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public EditModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        [Required(ErrorMessage = "Please enter a name for the reminder")]
        [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
        public string Name { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Please select a date for the reminder")]
        [DisplayName("Reminder Date")]
        public DateOnly ReminderDate { get; set; }

        [BindProperty]
        public int ReminderId { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var reminder = await _context.Reminders
                .Where(r => r.Id == id && r.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (reminder == null)
            {
                return NotFound();
            }

            // Only allow editing of custom reminders
            if (reminder.IsAutomatic)
            {
                return NotFound();
            }

            ReminderId = reminder.Id;
            Name = reminder.Name;
            ReminderDate = reminder.ReminderDate;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            // Verify the reminder belongs to the current user and is not automatic
            var existingReminder = await _context.Reminders
                .Where(r => r.Id == ReminderId && r.UserId == currentUser.Id && !r.IsAutomatic)
                .FirstOrDefaultAsync();

            if (existingReminder == null)
            {
                return NotFound();
            }

            // Update the fields that can be changed
            existingReminder.Name = Name;
            existingReminder.ReminderDate = ReminderDate;
            existingReminder.UpdatedAt = DateTime.UtcNow; // Update the timestamp

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ReminderExists(ReminderId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            TempData["SuccessMessage"] = "Reminder saved successfully";

            return RedirectToPage("./Index");
        }

        private bool ReminderExists(int id)
        {
            return _context.Reminders.Any(e => e.Id == id);
        }
    }
}
