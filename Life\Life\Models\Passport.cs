using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class Passport
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        public DateOnly StartDate { get; set; }

        [Required]
        public DateOnly ExpiryDate { get; set; }
        
        [Required]
        [StringLength(10)]
        public string PassportNumber { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal? Cost { get; set; }
        
        [StringLength(20)]
        public string? Size { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public Reminder? Reminder { get; set; }
    }
}
