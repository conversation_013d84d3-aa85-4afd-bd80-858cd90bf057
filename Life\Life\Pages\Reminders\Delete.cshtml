@page
@model Life.Pages.Reminders.DeleteModel
@{
    ViewData["Title"] = "Delete Reminder";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Delete Reminder</h2>
                </div>
                <div class="card-body text-center">
                    <h5 class="mt-3">Confirm deletion of reminder</h5>
                    <h3 class="text-">@Model.Name</h3>
                    <form method="post" class="row g-3 mt-1">
						<input type="hidden" asp-for="ReminderId" />
                        <div class="row my-3">
                            <div class="col-sm-12">
                                <button type="submit" class="btn btn-sm btn-danger">Confirm Deletion</button>
                                <a asp-page="./Index" class="btn btn-sm btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
