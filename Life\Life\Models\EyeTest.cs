using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class EyeTest
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        [StringLength(50)]
        public string Provider { get; set; } = string.Empty;
        
        [Required]
        public DateOnly TestDate { get; set; }

        public DateOnly? NextTestDate { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? RightSPH { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? RightCYL { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? RightAXIS { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? LeftSPH { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? LeftCYL { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? LeftAXIS { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Cost { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public Reminder? Reminder { get; set; }
    }
}
