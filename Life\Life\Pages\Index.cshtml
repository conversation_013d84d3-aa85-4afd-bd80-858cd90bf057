﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Life";
}



<div class="container">

    <section class="section min-vh-100 d-flex flex-column align-items-center justify-content-center py-4">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-4 col-md-6 d-flex flex-column align-items-center justify-content-center">


                    @if (TempData["LogoutMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                            @TempData["LogoutMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }
                    <div class="card mb-3">

                        <div class="card-body">

                            <div class="d-flex justify-content-center pt-4">
                                <h5>
                                    <a href="~/" class="d-flex align-items-center w-auto">
                                        <img src="~/logo.png" alt="Life" style="max-height: 80px;">
                                    </a>
                                </h5>
                            </div>

                            <div class="pb-2">
                                <h5 class="card-title text-center fs-4">Welcome!</h5>
                                <p>Life app is a personal admin tool for easy access to all your important information.</p>
                            </div>


                            <div class="row">
                                <div class="col-6">
                                    <a class="btn btn-primary w-100" asp-page="/Login">Login</a>
                                </div>

                                <div class="col-6">
                                    <a class="btn btn-secondary w-100" asp-page="/Register">Register </a>
                                </div>
                            </div>


                        </div>
                    </div>

                </div>
            </div>
        </div>

    </section>

</div>