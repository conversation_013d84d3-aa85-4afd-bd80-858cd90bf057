using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class VehicleMot
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        public int VehicleId { get; set; }
        
        [ForeignKey("VehicleId")]
        public Vehicle Vehicle { get; set; } = null!;
        
        [Required]
        public DateOnly TestDate { get; set; }

        [Required]
        public DateOnly ExpiryDate { get; set; }
        
        [StringLength(50)]
        public string? TestCentre { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Cost { get; set; }
        
        public int? Mileage { get; set; }
        
        [StringLength(2000)]
        public string? Advisories { get; set; }
        
        [StringLength(50)]
        public string? PaymentMethod { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public Reminder? Reminder { get; set; }
    }
}
