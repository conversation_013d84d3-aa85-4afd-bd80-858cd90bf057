using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class SavingsAccountPotBalanceHistoryRecord
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        public int SavingsAccountPotId { get; set; }
        
        [ForeignKey("SavingsAccountPotId")]
        public SavingsAccountPot SavingsAccountPot { get; set; } = null!;
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PotBalance { get; set; }
        
        [Required]
        public DateOnly Date { get; set; }
    }
}
