using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class VehicleInsuranceClaim
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        public int VehicleInsurancePolicyId { get; set; }
        
        [ForeignKey("VehicleInsurancePolicyId")]
        public VehicleInsurancePolicy VehicleInsurancePolicy { get; set; } = null!;
        
        [Required]
        public DateOnly ClaimDate { get; set; }
        
        [StringLength(50)]
        public string? ClaimReference { get; set; }
        
        [StringLength(2000)]
        public string? ClaimDetails { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
