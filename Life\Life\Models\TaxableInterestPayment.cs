using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class TaxableInterestPayment
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        public int? CurrentAccountId { get; set; }
        
        [ForeignKey("CurrentAccountId")]
        public CurrentAccount? CurrentAccount { get; set; }
        
        public int? SavingsAccountId { get; set; }
        
        [ForeignKey("SavingsAccountId")]
        public SavingsAccount? SavingsAccount { get; set; }
        
        public int? SavingsAccountPotId { get; set; }
        
        [ForeignKey("SavingsAccountPotId")]
        public SavingsAccountPot? SavingsAccountPot { get; set; }
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal InterestAmount { get; set; }
        
        [Required]
        public DateOnly Date { get; set; }
    }
}
