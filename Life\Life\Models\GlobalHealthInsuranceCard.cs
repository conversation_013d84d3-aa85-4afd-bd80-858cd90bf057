using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class GlobalHealthInsuranceCard
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        public DateOnly StartDate { get; set; }

        [Required]
        public DateOnly ExpiryDate { get; set; }
        
        [Required]
        [StringLength(50)]
        public string PersonalIdNumber { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string CardIdNumber { get; set; } = string.Empty;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public Reminder? Reminder { get; set; }
    }
}
