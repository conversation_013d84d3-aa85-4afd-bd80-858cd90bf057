using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class RecurringPayment
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        public int? CreditCardId { get; set; }
        
        [ForeignKey("CreditCardId")]
        public CreditCard? CreditCard { get; set; }
        
        public int? DebitCardId { get; set; }
        
        [ForeignKey("DebitCardId")]
        public DebitCard? DebitCard { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? PaymentAmount { get; set; }
        
        [StringLength(20)]
        public string? PaymentFrequency { get; set; }
        
        [StringLength(4)]
        public string? PaymentDayOfMonth { get; set; }
        
        [StringLength(20)]
        public string? PaymentMonth { get; set; }
        
        public DateOnly? StartDate { get; set; }

        public DateOnly? NextPaymentDate { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public Reminder? Reminder { get; set; }
    }
}
