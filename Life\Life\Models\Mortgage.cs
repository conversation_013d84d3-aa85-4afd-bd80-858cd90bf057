using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class Mortgage
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        public int AddressId { get; set; }
        
        [ForeignKey("AddressId")]
        public Address Address { get; set; } = null!;
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string Provider { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? Reference { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? ArrangementFee { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? BookingFee { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? RemittanceFee { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? ValuationFee { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? FixedTermApr { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? StandardVariableRate { get; set; }
        
        public int? TotalTermMonths { get; set; }
        
        public int? FixedTermMonths { get; set; }
        
        public DateOnly? StartDate { get; set; }

        public DateOnly? FixedTermEndDate { get; set; }

        public DateOnly? EndDate { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? PaymentAmount { get; set; }
        
        [StringLength(4)]
        public string? PaymentDayOfMonth { get; set; }
        
        [StringLength(50)]
        public string? Type { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? EarlyRepaymentCharge { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? RemainingBalance { get; set; }
        
        [Required]
        public bool Active { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public DirectDebit? DirectDebit { get; set; }
        public StandingOrder? StandingOrder { get; set; }
        public ICollection<Reminder> Reminders { get; set; } = new List<Reminder>();
    }
}
