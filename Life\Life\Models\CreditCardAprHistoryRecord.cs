using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class CreditCardAprHistoryRecord
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        public int CreditCardId { get; set; }
        
        [ForeignKey("CreditCardId")]
        public CreditCard CreditCard { get; set; } = null!;
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Apr { get; set; }
        
        [Required]
        public DateOnly Date { get; set; }
    }
}
