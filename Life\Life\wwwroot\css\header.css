﻿.header-date {
    margin: auto 1rem;
}

.logo {
    line-height: 1;
}

@media (min-width: 1200px) {
    .logo {
        width: 240px;
    }
}

.logo img {
    max-height: 26px;
    margin-right: 6px;
}

.logo span {
    font-size: 26px;
    font-weight: 700;
    color: #012970;
    font-family: "N<PERSON><PERSON>", sans-serif;
}

.header {
    transition: all 0.5s;
    z-index: 997;
    height: 60px;
    box-shadow: 0px 2px 20px rgba(1, 41, 112, 0.1);
    background-color: #fff;
    padding-left: 20px;
    /* Toggle Sidebar Button */
    /* Search Bar */
}


    .header .toggle-sidebar-btn {
        font-size: 24px;
        padding-left: 10px;
        cursor: pointer;
        color: #012970;
    }

        .header .toggle-sidebar-btn:hover {
            color: #2d5eb1;
        }

@media (min-width: 1200px) {
    .header .toggle-sidebar-btn {
        visibility: hidden;
    }
}

.header .search-bar {
    min-width: 360px;
    padding: 0 20px;
}

/* @media (max-width: 1199px) {
    .header .search-bar {
      position: fixed;
      top: 50px;
      left: 0;
      right: 0;
      padding: 20px;
      box-shadow: 0px 0px 15px 0px rgba(1, 41, 112, 0.1);
      background: white;
      z-index: 9999;
      transition: 0.3s;
      visibility: hidden;
      opacity: 0;
    }
  
    .header .search-bar-show {
      top: 60px;
      visibility: visible;
      opacity: 1;
    }
  } */

.header .search-form {
    width: 100%;
}

    .header .search-form input {
        border: 0;
        font-size: 14px;
        color: #012970;
        border: 1px solid rgba(1, 41, 112, 0.2);
        padding: 7px 38px 7px 8px;
        border-radius: 3px;
        transition: 0.3s;
        width: 100%;
    }

        .header .search-form input:focus,
        .header .search-form input:hover {
            outline: none;
            box-shadow: 0 0 10px 0 rgba(1, 41, 112, 0.15);
            border: 1px solid rgba(1, 41, 112, 0.3);
        }

    .header .search-form button {
        border: 0;
        padding: 0;
        margin-left: -30px;
        background: none;
    }

        .header .search-form button i {
            color: #012970;
        }
