using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class DirectDebit
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        public int CurrentAccountId { get; set; }

        [ForeignKey("CurrentAccountId")]
        public CurrentAccount CurrentAccount { get; set; } = null!;

        public int? GeneralItemId { get; set; }

        [ForeignKey("GeneralItemId")]
        public GeneralItem? GeneralItem { get; set; }

        public int? VehicleTaxId { get; set; }

        [ForeignKey("VehicleTaxId")]
        public VehicleTax? VehicleTax { get; set; }

        public int? VehicleFinanceAgreementId { get; set; }

        [ForeignKey("VehicleFinanceAgreementId")]
        public VehicleFinanceAgreement? VehicleFinanceAgreement { get; set; }

        public int? LoanId { get; set; }
        
        [ForeignKey("LoanId")]
        public Loan? Loan { get; set; }
        
        public int? MortgageId { get; set; }
        
        [ForeignKey("MortgageId")]
        public Mortgage? Mortgage { get; set; }
        
        public int? LifeInsurancePolicyId { get; set; }
        
        [ForeignKey("LifeInsurancePolicyId")]
        public LifeInsurancePolicy? LifeInsurancePolicy { get; set; }
        
        public int? TravelInsurancePolicyId { get; set; }
        
        [ForeignKey("TravelInsurancePolicyId")]
        public TravelInsurancePolicy? TravelInsurancePolicy { get; set; }
        
        public int? GadgetInsurancePolicyId { get; set; }
        
        [ForeignKey("GadgetInsurancePolicyId")]
        public GadgetInsurancePolicy? GadgetInsurancePolicy { get; set; }
        
        public int? VehicleInsurancePolicyId { get; set; }
        
        [ForeignKey("VehicleInsurancePolicyId")]
        public VehicleInsurancePolicy? VehicleInsurancePolicy { get; set; }
        
        public int? AddressInsurancePolicyId { get; set; }
        
        [ForeignKey("AddressInsurancePolicyId")]
        public AddressInsurancePolicy? AddressInsurancePolicy { get; set; }
        
        public int? TenancyId { get; set; }
        
        [ForeignKey("TenancyId")]
        public Tenancy? Tenancy { get; set; }
        
        public int? UtilityBillId { get; set; }
        
        [ForeignKey("UtilityBillId")]
        public UtilityBill? UtilityBill { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? PaymentAmount { get; set; }

        [StringLength(50)]
        public string? PaymentFrequency { get; set; }

        [StringLength(4)]
        public string? PaymentDayOfMonth { get; set; }

        [StringLength(20)]
        public string? PaymentMonth { get; set; }

        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
