using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Life.Models
{
    public class Address
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(225)]
        public string UserId { get; set; } = string.Empty;
        
        [ForeignKey("UserId")]
        public User User { get; set; } = null!;
        
        [Required]
        [StringLength(20)]
        public string HouseFlatNumber { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string StreetLineOne { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string? StreetLineTwo { get; set; }
        
        [Required]
        [StringLength(100)]
        public string City { get; set; } = string.Empty;
        
        [Required]
        [StringLength(20)]
        public string Postcode { get; set; } = string.Empty;
        
        public DateOnly? MoveInDate { get; set; }

        public DateOnly? MoveOutDate { get; set; }

        public DateOnly? PurchaseDate { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? PurchasePrice { get; set; }

        public DateOnly? SoldDate { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? SoldPrice { get; set; }
        
        [StringLength(2000)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties - only entities that have foreign keys pointing to Address
        public ICollection<Vehicle> Vehicles { get; set; } = new List<Vehicle>();
        public ICollection<GeneralItem> GeneralItems { get; set; } = new List<GeneralItem>();
        public ICollection<DrivingLicence> DrivingLicences { get; set; } = new List<DrivingLicence>();
        public ICollection<GpPractice> GpPractices { get; set; } = new List<GpPractice>();
        public ICollection<Dentist> Dentists { get; set; } = new List<Dentist>();
        public ICollection<CurrentAccount> CurrentAccounts { get; set; } = new List<CurrentAccount>();
        public ICollection<CreditCard> CreditCards { get; set; } = new List<CreditCard>();
        public ICollection<SavingsAccount> SavingsAccounts { get; set; } = new List<SavingsAccount>();
        public ICollection<Loan> Loans { get; set; } = new List<Loan>();
        public ICollection<Mortgage> Mortgages { get; set; } = new List<Mortgage>();
        public ICollection<Pension> Pensions { get; set; } = new List<Pension>();
        public ICollection<LifeInsurancePolicy> LifeInsurancePolicies { get; set; } = new List<LifeInsurancePolicy>();
        public ICollection<TravelInsurancePolicy> TravelInsurancePolicies { get; set; } = new List<TravelInsurancePolicy>();
        public ICollection<GadgetInsurancePolicy> GadgetInsurancePolicies { get; set; } = new List<GadgetInsurancePolicy>();
        public ICollection<VehicleInsurancePolicy> VehicleInsurancePolicies { get; set; } = new List<VehicleInsurancePolicy>();
        public ICollection<AddressInsurancePolicy> AddressInsurancePolicies { get; set; } = new List<AddressInsurancePolicy>();
        public ICollection<VehicleFinanceAgreement> VehicleFinanceAgreements { get; set; } = new List<VehicleFinanceAgreement>();
        public ICollection<VehicleBreakdownPolicy> VehicleBreakdownPolicies { get; set; } = new List<VehicleBreakdownPolicy>();
        public ICollection<Tenancy> Tenancies { get; set; } = new List<Tenancy>();
        public ICollection<UtilityBill> UtilityBills { get; set; } = new List<UtilityBill>();
    }
}
