using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace Life.Pages
{
    public class AccountModel : PageModel
    {
        private readonly UserManager<User> _userManager;

        public AccountModel(UserManager<User> userManager)
        {
            _userManager = userManager;
        }

        public User? CurrentUser { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = await _userManager.GetUserAsync(User);
            if (CurrentUser == null)
            {
                return RedirectToPage("/Login");
            }

            return Page();
        }

    }
}
