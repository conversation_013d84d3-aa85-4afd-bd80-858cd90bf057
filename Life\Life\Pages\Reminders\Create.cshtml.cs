using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages.Reminders
{
    public class CreateModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public CreateModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public IActionResult OnGet()
        {
            ReminderDate = DateOnly.FromDateTime(DateTime.UtcNow);

            return Page();
        }

        [BindProperty]
        [Required(ErrorMessage = "Please enter a name for the reminder")]
        [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
        public string Name { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Please select a date for the reminder")]
        [DisplayName("Reminder Date")]
        public DateOnly ReminderDate { get; set; }

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            //if (ReminderDate < DateOnly.FromDateTime(DateTime.UtcNow))
            //{
            //    ModelState.AddModelError(nameof(ReminderDate), "Reminder date cannot be in the past");
            //}

            if (!ModelState.IsValid)
            {
                return Page();
            }

            Reminder Reminder = new Reminder
            {
                Name = Name,
                ReminderDate = ReminderDate,
                User = currentUser,
                UserId = currentUser.Id,
                IsAutomatic = false, // This is a custom reminder
                Number = 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            

            _context.Reminders.Add(Reminder);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Reminder created successfully";

            return RedirectToPage("./Index");
        }
    }
}
