﻿using Microsoft.AspNetCore.Identity;

namespace Life.Models
{
    public class User : IdentityUser
    {
        public DateTime DateCreated { get; set; } = DateTime.UtcNow;

        public ICollection<Address> Addresses { get; set; } = new List<Address>();
        public ICollection<GeneralItem> GeneralItems { get; set; } = new List<GeneralItem>();
        public ICollection<Vehicle> Vehicles { get; set; } = new List<Vehicle>();
        public ICollection<DrivingLicence> DrivingLicences { get; set; } = new List<DrivingLicence>();
        public ICollection<Passport> Passports { get; set; } = new List<Passport>();
        public ICollection<GpPractice> GpPractices { get; set; } = new List<GpPractice>();
        public ICollection<Dentist> Dentists { get; set; } = new List<Dentist>();
        public ICollection<EyeTest> EyeTests { get; set; } = new List<EyeTest>();
        public ICollection<LifeInsurancePolicy> LifeInsurancePolicies { get; set; } = new List<LifeInsurancePolicy>();
        public ICollection<CurrentAccount> CurrentAccounts { get; set; } = new List<CurrentAccount>();
        public ICollection<CurrentAccountAprHistoryRecord> CurrentAccountAprHistoryRecords { get; set; } = new List<CurrentAccountAprHistoryRecord>();
        public ICollection<CreditCard> CreditCards { get; set; } = new List<CreditCard>();
        public ICollection<CreditCardAprHistoryRecord> CreditCardAprHistoryRecords { get; set; } = new List<CreditCardAprHistoryRecord>();
        public ICollection<SavingsAccount> SavingsAccounts { get; set; } = new List<SavingsAccount>();
        public ICollection<SavingsAccountAprHistoryRecord> SavingsAccountAprHistoryRecords { get; set; } = new List<SavingsAccountAprHistoryRecord>();
        public ICollection<SavingsAccountBalanceHistoryRecord> SavingsAccountBalanceHistoryRecords { get; set; } = new List<SavingsAccountBalanceHistoryRecord>();
        public ICollection<SavingsAccountPot> SavingsAccountPots { get; set; } = new List<SavingsAccountPot>();
        public ICollection<SavingsAccountPotAprHistoryRecord> SavingsAccountPotAprHistoryRecords { get; set; } = new List<SavingsAccountPotAprHistoryRecord>();
        public ICollection<SavingsAccountPotBalanceHistoryRecord> SavingsAccountPotBalanceHistoryRecords { get; set; } = new List<SavingsAccountPotBalanceHistoryRecord>();
        public ICollection<TaxableInterestPayment> TaxableInterestPayments { get; set; } = new List<TaxableInterestPayment>();
        public ICollection<Loan> Loans { get; set; } = new List<Loan>();
        public ICollection<Mortgage> Mortgages { get; set; } = new List<Mortgage>();
        public ICollection<Pension> Pensions { get; set; } = new List<Pension>();
        public ICollection<PensionBalanceHistoryRecord> PensionBalanceHistoryRecords { get; set; } = new List<PensionBalanceHistoryRecord>();
        public ICollection<DebitCard> DebitCards { get; set; } = new List<DebitCard>();
        public ICollection<RecurringPayment> RecurringPayments { get; set; } = new List<RecurringPayment>();
        public ICollection<DirectDebit> DirectDebits { get; set; } = new List<DirectDebit>();
        public ICollection<StandingOrder> StandingOrders { get; set; } = new List<StandingOrder>();
        public ICollection<TravelInsurancePolicy> TravelInsurancePolicies { get; set; } = new List<TravelInsurancePolicy>();
        public ICollection<GadgetInsurancePolicy> GadgetInsurancePolicies { get; set; } = new List<GadgetInsurancePolicy>();
        public ICollection<GlobalHealthInsuranceCard> GlobalHealthInsuranceCards { get; set; } = new List<GlobalHealthInsuranceCard>();
        public ICollection<VehicleInsurancePolicy> VehicleInsurancePolicies { get; set; } = new List<VehicleInsurancePolicy>();
        public ICollection<VehicleInsuranceClaim> VehicleInsuranceClaims { get; set; } = new List<VehicleInsuranceClaim>();
        public ICollection<VehicleBreakdownPolicy> VehicleBreakdownPolicies { get; set; } = new List<VehicleBreakdownPolicy>();
        public ICollection<VehicleMot> VehicleMots { get; set; } = new List<VehicleMot>();
        public ICollection<VehicleService> VehicleServices { get; set; } = new List<VehicleService>();
        public ICollection<VehicleTax> VehicleTaxes { get; set; } = new List<VehicleTax>();
        public ICollection<VehicleWarranty> VehicleWarranties { get; set; } = new List<VehicleWarranty>();
        public ICollection<VehicleServicePlan> VehicleServicePlans { get; set; } = new List<VehicleServicePlan>();
        public ICollection<VehicleFinanceAgreement> VehicleFinanceAgreements { get; set; } = new List<VehicleFinanceAgreement>();
        public ICollection<AddressInsurancePolicy> AddressInsurancePolicies { get; set; } = new List<AddressInsurancePolicy>();
        public ICollection<Tenancy> Tenancies { get; set; } = new List<Tenancy>();
        public ICollection<UtilityBill> UtilityBills { get; set; } = new List<UtilityBill>();
        public ICollection<Reminder> Reminders { get; set; } = new List<Reminder>();
    }




}
