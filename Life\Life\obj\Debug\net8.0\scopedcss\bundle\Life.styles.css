/* _content/Life/Pages/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-zlca64hrbn] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-zlca64hrbn] {
  color: #0077cc;
}

.btn-primary[b-zlca64hrbn] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-zlca64hrbn], .nav-pills .show > .nav-link[b-zlca64hrbn] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-zlca64hrbn] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-zlca64hrbn] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-zlca64hrbn] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-zlca64hrbn] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-zlca64hrbn] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
